/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Dashboard.tsx */ \"(rsc)/./src/components/dashboard/Dashboard.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRjIwMjUtY3J5cHRvLXNpbmFpcy1haSUyRmNyeXB0by1zaWduYWxzLWFpJTJGc3JjJTJGY29tcG9uZW50cyUyRmRhc2hib2FyZCUyRkRhc2hib2FyZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJEYXNoYm9hcmQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFvTSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRGFzaGJvYXJkXCJdICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy8yMDI1LWNyeXB0by1zaW5haXMtYWkvY3J5cHRvLXNpZ25hbHMtYWkvc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbGV4YW5kcmVzaW1hc21hY2llbC9Eb2N1bWVudHMvaWEtc2lzdGVtYXMvMjAyNS1jcnlwdG8tc2luYWlzLWFpL2NyeXB0by1zaWduYWxzLWFpL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"95b9e9695b60\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWxleGFuZHJlc2ltYXNtYWNpZWwvRG9jdW1lbnRzL2lhLXNpc3RlbWFzLzIwMjUtY3J5cHRvLXNpbmFpcy1haS9jcnlwdG8tc2lnbmFscy1haS9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTViOWU5Njk1YjYwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbGV4YW5kcmVzaW1hc21hY2llbC9Eb2N1bWVudHMvaWEtc2lzdGVtYXMvMjAyNS1jcnlwdG8tc2luYWlzLWFpL2NyeXB0by1zaWduYWxzLWFpL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/Dashboard */ \"(rsc)/./src/components/dashboard/Dashboard.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_1__.Dashboard, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTREO0FBRTdDLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxzRUFBU0E7Ozs7O0FBQ25CIiwic291cmNlcyI6WyIvVXNlcnMvYWxleGFuZHJlc2ltYXNtYWNpZWwvRG9jdW1lbnRzL2lhLXNpc3RlbWFzLzIwMjUtY3J5cHRvLXNpbmFpcy1haS9jcnlwdG8tc2lnbmFscy1haS9zcmMvYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IERhc2hib2FyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gPERhc2hib2FyZCAvPlxufVxuIl0sIm5hbWVzIjpbIkRhc2hib2FyZCIsIkhvbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Dashboard: () => (/* binding */ Dashboard)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Dashboard = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Dashboard() from the server but Dashboard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx",
"Dashboard",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/dashboard/Dashboard.tsx */ \"(ssr)/./src/components/dashboard/Dashboard.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxleGFuZHJlc2ltYXNtYWNpZWwlMkZEb2N1bWVudHMlMkZpYS1zaXN0ZW1hcyUyRjIwMjUtY3J5cHRvLXNpbmFpcy1haSUyRmNyeXB0by1zaWduYWxzLWFpJTJGc3JjJTJGY29tcG9uZW50cyUyRmRhc2hib2FyZCUyRkRhc2hib2FyZC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJEYXNoYm9hcmQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFvTSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRGFzaGJvYXJkXCJdICovIFwiL1VzZXJzL2FsZXhhbmRyZXNpbWFzbWFjaWVsL0RvY3VtZW50cy9pYS1zaXN0ZW1hcy8yMDI1LWNyeXB0by1zaW5haXMtYWkvY3J5cHRvLXNpZ25hbHMtYWkvc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL0Rhc2hib2FyZC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fcomponents%2Fdashboard%2FDashboard.tsx%22%2C%22ids%22%3A%5B%22Dashboard%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/charts/MarketHeatmap.tsx":
/*!*************************************************!*\
  !*** ./src/components/charts/MarketHeatmap.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketHeatmap: () => (/* binding */ MarketHeatmap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketHeatmap auto */ \n\n\n\nconst mockHeatmapData = [\n    {\n        symbol: 'BTC',\n        name: 'Bitcoin',\n        price: 52350,\n        change24h: 2.3,\n        marketCap: 1020000000000,\n        volume24h: 28000000000\n    },\n    {\n        symbol: 'ETH',\n        name: 'Ethereum',\n        price: 3180,\n        change24h: 1.8,\n        marketCap: 382000000000,\n        volume24h: 15000000000\n    },\n    {\n        symbol: 'BNB',\n        name: 'BNB',\n        price: 315,\n        change24h: -0.5,\n        marketCap: 47000000000,\n        volume24h: 1200000000\n    },\n    {\n        symbol: 'SOL',\n        name: 'Solana',\n        price: 98.5,\n        change24h: 4.2,\n        marketCap: 45000000000,\n        volume24h: 2100000000\n    },\n    {\n        symbol: 'XRP',\n        name: 'XRP',\n        price: 0.52,\n        change24h: -1.2,\n        marketCap: 29000000000,\n        volume24h: 1800000000\n    },\n    {\n        symbol: 'ADA',\n        name: 'Cardano',\n        price: 0.38,\n        change24h: 0.8,\n        marketCap: 13000000000,\n        volume24h: 450000000\n    },\n    {\n        symbol: 'AVAX',\n        name: 'Avalanche',\n        price: 28.5,\n        change24h: 3.1,\n        marketCap: 11000000000,\n        volume24h: 380000000\n    },\n    {\n        symbol: 'DOT',\n        name: 'Polkadot',\n        price: 6.2,\n        change24h: -0.8,\n        marketCap: 8500000000,\n        volume24h: 220000000\n    },\n    {\n        symbol: 'MATIC',\n        name: 'Polygon',\n        price: 0.85,\n        change24h: 2.1,\n        marketCap: 8200000000,\n        volume24h: 340000000\n    },\n    {\n        symbol: 'LINK',\n        name: 'Chainlink',\n        price: 14.2,\n        change24h: 1.5,\n        marketCap: 8800000000,\n        volume24h: 280000000\n    },\n    {\n        symbol: 'UNI',\n        name: 'Uniswap',\n        price: 8.9,\n        change24h: -2.1,\n        marketCap: 6700000000,\n        volume24h: 180000000\n    },\n    {\n        symbol: 'LTC',\n        name: 'Litecoin',\n        price: 92,\n        change24h: 0.3,\n        marketCap: 6900000000,\n        volume24h: 420000000\n    },\n    {\n        symbol: 'ATOM',\n        name: 'Cosmos',\n        price: 7.8,\n        change24h: 1.9,\n        marketCap: 3100000000,\n        volume24h: 95000000\n    },\n    {\n        symbol: 'FTM',\n        name: 'Fantom',\n        price: 0.42,\n        change24h: 5.2,\n        marketCap: 1200000000,\n        volume24h: 85000000\n    },\n    {\n        symbol: 'ALGO',\n        name: 'Algorand',\n        price: 0.18,\n        change24h: -1.5,\n        marketCap: 1400000000,\n        volume24h: 45000000\n    },\n    {\n        symbol: 'VET',\n        name: 'VeChain',\n        price: 0.025,\n        change24h: 0.9,\n        marketCap: 1800000000,\n        volume24h: 32000000\n    }\n];\nfunction MarketHeatmap() {\n    const getChangeColor = (change)=>{\n        if (change > 3) return 'bg-green-500';\n        if (change > 1) return 'bg-green-400';\n        if (change > 0) return 'bg-green-300';\n        if (change > -1) return 'bg-red-300';\n        if (change > -3) return 'bg-red-400';\n        return 'bg-red-500';\n    };\n    const getTextColor = (change)=>{\n        return Math.abs(change) > 1 ? 'text-white' : 'text-gray-800';\n    };\n    const getSize = (marketCap)=>{\n        // Normalize market cap to determine size\n        const maxCap = Math.max(...mockHeatmapData.map((d)=>d.marketCap));\n        const minCap = Math.min(...mockHeatmapData.map((d)=>d.marketCap));\n        const normalized = (marketCap - minCap) / (maxCap - minCap);\n        // Return size classes based on normalized value\n        if (normalized > 0.8) return 'col-span-4 row-span-3';\n        if (normalized > 0.6) return 'col-span-3 row-span-2';\n        if (normalized > 0.4) return 'col-span-2 row-span-2';\n        if (normalized > 0.2) return 'col-span-2 row-span-1';\n        return 'col-span-1 row-span-1';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"glass-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Market Heatmap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Size represents market cap, color represents 24h change\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-8 grid-rows-6 gap-2 h-96\",\n                        children: mockHeatmapData.map((crypto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `\n                ${getSize(crypto.marketCap)}\n                ${getChangeColor(crypto.change24h)}\n                ${getTextColor(crypto.change24h)}\n                rounded-lg p-3 flex flex-col justify-between\n                transition-all duration-300 hover:scale-105 hover:z-10\n                cursor-pointer relative group\n              `,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 whitespace-nowrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold\",\n                                                children: crypto.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Price: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.price)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Market Cap: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.marketCap)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Volume: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.volume24h)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"24h Change: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPercentage)(crypto.change24h)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-sm mb-1\",\n                                                        children: crypto.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-80\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-sm\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPercentage)(crypto.change24h)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, crypto.symbol, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex items-center justify-between text-sm text-white/60\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"24h Change:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-red-500 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"< -3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-red-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"-1% to -3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"0% to 1%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-400 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"1% to 3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-500 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"> 3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Size = Market Cap\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/charts/MarketHeatmap.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/CryptoMetrics.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/CryptoMetrics.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoMetrics: () => (/* binding */ CryptoMetrics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CryptoMetrics auto */ \n\n\n\n\n\nconst mockFundingRates = [\n    {\n        exchange: 'Binance',\n        symbol: 'BTCUSDT',\n        rate: 0.0125,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Bybit',\n        symbol: 'BTCUSDT',\n        rate: 0.0089,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'OKX',\n        symbol: 'BTCUSDT',\n        rate: 0.0156,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Binance',\n        symbol: 'ETHUSDT',\n        rate: 0.0078,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Bybit',\n        symbol: 'ETHUSDT',\n        rate: 0.0092,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'OKX',\n        symbol: 'ETHUSDT',\n        rate: 0.0134,\n        nextFunding: '4h 23m'\n    }\n];\nconst mockLiquidations = [\n    {\n        timeframe: '1h',\n        longs: 12500000,\n        shorts: 8200000,\n        total: 20700000\n    },\n    {\n        timeframe: '4h',\n        longs: 45000000,\n        shorts: 28000000,\n        total: 73000000\n    },\n    {\n        timeframe: '24h',\n        longs: 180000000,\n        shorts: 95000000,\n        total: 275000000\n    }\n];\nconst mockOnChainData = [\n    {\n        metric: 'Exchange Inflow',\n        value: '-2,450 BTC',\n        change24h: -15.2,\n        status: 'bullish'\n    },\n    {\n        metric: 'Exchange Outflow',\n        value: '+3,890 BTC',\n        change24h: 22.8,\n        status: 'bullish'\n    },\n    {\n        metric: 'Whale Transactions',\n        value: '1,247',\n        change24h: 8.5,\n        status: 'neutral'\n    },\n    {\n        metric: 'Active Addresses',\n        value: '985,432',\n        change24h: 3.2,\n        status: 'bullish'\n    },\n    {\n        metric: 'Network Hash Rate',\n        value: '450.2 EH/s',\n        change24h: 1.8,\n        status: 'bullish'\n    },\n    {\n        metric: 'Mining Difficulty',\n        value: '62.46 T',\n        change24h: 0.5,\n        status: 'neutral'\n    }\n];\nfunction CryptoMetrics() {\n    const getFundingRateColor = (rate)=>{\n        if (rate > 0.02) return 'text-red-400';\n        if (rate > 0.01) return 'text-yellow-400';\n        if (rate > 0) return 'text-green-400';\n        if (rate > -0.01) return 'text-blue-400';\n        return 'text-purple-400';\n    };\n    const getFundingRateStatus = (rate)=>{\n        if (rate > 0.02) return 'Extreme Greed';\n        if (rate > 0.01) return 'Greed';\n        if (rate > 0) return 'Bullish';\n        if (rate > -0.01) return 'Bearish';\n        return 'Extreme Fear';\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'bullish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, this);\n            case 'bearish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const averageFundingBTC = mockFundingRates.filter((f)=>f.symbol === 'BTCUSDT').reduce((sum, f)=>sum + f.rate, 0) / 3;\n    const averageFundingETH = mockFundingRates.filter((f)=>f.symbol === 'ETHUSDT').reduce((sum, f)=>sum + f.rate, 0) / 3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Funding Rates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"border-white/20 text-white/70\",\n                                    children: \"Live\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: \"BTC/USDT Average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-bold ${getFundingRateColor(averageFundingBTC)}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(averageFundingBTC * 100, 4)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: [\n                                            \"Status: \",\n                                            getFundingRateStatus(averageFundingBTC)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: \"ETH/USDT Average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-bold ${getFundingRateColor(averageFundingETH)}`,\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(averageFundingETH * 100, 4)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: [\n                                            \"Status: \",\n                                            getFundingRateStatus(averageFundingETH)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"By Exchange\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    mockFundingRates.map((rate, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/60\",\n                                                            children: rate.exchange\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/80\",\n                                                            children: rate.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: getFundingRateColor(rate.rate),\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(rate.rate * 100, 4)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/40 text-xs\",\n                                                            children: rate.nextFunding\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Liquidations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: mockLiquidations.map((liq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: liq.timeframe\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.total)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-400 font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.longs)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Longs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-400 font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.shorts)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Shorts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-2 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-400\",\n                                                        style: {\n                                                            width: `${liq.longs / liq.total * 100}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-400\",\n                                                        style: {\n                                                            width: `${liq.shorts / liq.total * 100}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (liq.longs / liq.total * 100).toFixed(1),\n                                                            \"% Longs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (liq.shorts / liq.total * 100).toFixed(1),\n                                                            \"% Shorts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card lg:col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"On-Chain Metrics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: mockOnChainData.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-white/5 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white/60\",\n                                                    children: metric.metric\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                getStatusIcon(metric.status)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-white mb-1\",\n                                            children: metric.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                metric.change24h >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-sm ${metric.change24h >= 0 ? 'text-green-400' : 'text-red-400'}`,\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(metric.change24h),\n                                                        \" 24h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/CryptoMetrics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(ssr)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../signals/SignalCard */ \"(ssr)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(ssr)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(ssr)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CryptoMetrics */ \"(ssr)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    const [signals, setSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockSignals);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockMarketData);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setLastUpdate(new Date());\n        setIsRefreshing(false);\n    };\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketData.marketCapChange24h > 0) score += 10;\n        if (marketData.fearGreedIndex > 50) score += 10;\n        if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = activeSignals.filter((s)=>s.type === 'buy');\n    const sellSignals = activeSignals.filter((s)=>s.type === 'sell');\n    const alertSignals = activeSignals.filter((s)=>s.type === 'alert');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: `h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `px-4 py-2 rounded-lg ${healthStatus.bg}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `font-bold text-lg ${healthStatus.color}`,\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {\n                    marketCap: marketData.totalMarketCap,\n                    marketCapChange: marketData.marketCapChange24h,\n                    volume24h: marketData.totalVolume24h,\n                    btcDominance: marketData.btcDominance,\n                    fearGreedIndex: marketData.fearGreedIndex,\n                    activeCoins: marketData.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Dashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/MarketOverview.tsx":
/*!*****************************************************!*\
  !*** ./src/components/dashboard/MarketOverview.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketOverview: () => (/* binding */ MarketOverview),\n/* harmony export */   glassCardStyles: () => (/* binding */ glassCardStyles)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,DollarSign,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketOverview,glassCardStyles auto */ \n\n\n\n\n\nfunction MarketOverview({ marketCap, marketCapChange, volume24h, btcDominance, fearGreedIndex, activeCoins }) {\n    const getMarketSentiment = (score)=>{\n        if (score >= 75) return {\n            label: 'Extreme Greed',\n            color: 'bg-green-500',\n            icon: _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        };\n        if (score >= 55) return {\n            label: 'Greed',\n            color: 'bg-green-400',\n            icon: _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        };\n        if (score >= 45) return {\n            label: 'Neutral',\n            color: 'bg-yellow-500',\n            icon: _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        };\n        if (score >= 25) return {\n            label: 'Fear',\n            color: 'bg-red-400',\n            icon: _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        };\n        return {\n            label: 'Extreme Fear',\n            color: 'bg-red-500',\n            icon: _barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        };\n    };\n    const sentiment = getMarketSentiment(fearGreedIndex);\n    const SentimentIcon = sentiment.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium text-white/80\",\n                                children: \"Total Market Cap\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4 text-white/60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(marketCap, 'USD')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mt-2\",\n                                children: [\n                                    marketCapChange >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm ${marketCapChange >= 0 ? 'text-green-400' : 'text-red-400'}`,\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(marketCapChange)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium text-white/80\",\n                                children: \"24h Volume\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_DollarSign_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-4 w-4 text-white/60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(volume24h, 'USD')\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-white/60 mt-2\",\n                                children: [\n                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(activeCoins),\n                                    \" active coins\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium text-white/80\",\n                                children: \"BTC Dominance\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-bold text-white\",\n                                    children: \"₿\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-white\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(btcDominance, 1)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-white/20 rounded-full h-2 mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${btcDominance}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm font-medium text-white/80\",\n                                children: \"Fear & Greed Index\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SentimentIcon, {\n                                className: \"h-4 w-4 text-white/60\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: fearGreedIndex\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        className: `${sentiment.color} text-white border-none`,\n                                        children: sentiment.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-white/20 rounded-full h-2 mt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${sentiment.color} h-2 rounded-full transition-all duration-300`,\n                                    style: {\n                                        width: `${fearGreedIndex}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/MarketOverview.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n// Glass card styles to be added to globals.css\nconst glassCardStyles = `\n.glass-card {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);\n}\n\n.glass-card:hover {\n  background: rgba(255, 255, 255, 0.15);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  transform: translateY(-2px);\n  transition: all 0.3s ease;\n}\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/MarketOverview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/indicators/IndicatorMatrix.tsx":
/*!*******************************************************!*\
  !*** ./src/components/indicators/IndicatorMatrix.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IndicatorMatrix: () => (/* binding */ IndicatorMatrix)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Filter,HelpCircle,Minus,Search,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ IndicatorMatrix auto */ \n\n\n\n\n// Mock indicator data - In real app, this would come from calculations\nconst mockIndicators = [\n    // Momentum Indicators\n    {\n        id: 'RSI',\n        name: 'RSI (14)',\n        type: 'momentum',\n        status: 'bullish',\n        value: 35.2,\n        strength: 78,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'MACD',\n        name: 'MACD (12,26,9)',\n        type: 'momentum',\n        status: 'bullish',\n        value: 'Bullish Cross',\n        strength: 85,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Stochastic',\n        name: 'Stochastic %K',\n        type: 'momentum',\n        status: 'neutral',\n        value: 52.1,\n        strength: 45,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Williams',\n        name: 'Williams %R',\n        type: 'momentum',\n        status: 'bullish',\n        value: -25.3,\n        strength: 72,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'CCI',\n        name: 'CCI (20)',\n        type: 'momentum',\n        status: 'bearish',\n        value: -120.5,\n        strength: 68,\n        lastUpdate: new Date()\n    },\n    // Trend Indicators\n    {\n        id: 'SMA20',\n        name: 'SMA (20)',\n        type: 'trend',\n        status: 'bullish',\n        value: 51850,\n        strength: 65,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'EMA50',\n        name: 'EMA (50)',\n        type: 'trend',\n        status: 'bullish',\n        value: 51200,\n        strength: 70,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'ParabolicSAR',\n        name: 'Parabolic SAR',\n        type: 'trend',\n        status: 'bullish',\n        value: 50800,\n        strength: 82,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Ichimoku',\n        name: 'Ichimoku Cloud',\n        type: 'trend',\n        status: 'bullish',\n        value: 'Above Cloud',\n        strength: 88,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'ADX',\n        name: 'ADX (14)',\n        type: 'trend',\n        status: 'neutral',\n        value: 28.5,\n        strength: 55,\n        lastUpdate: new Date()\n    },\n    // Volatility Indicators\n    {\n        id: 'BollingerBands',\n        name: 'Bollinger Bands',\n        type: 'volatility',\n        status: 'neutral',\n        value: 'Mid-Band',\n        strength: 40,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'ATR',\n        name: 'ATR (14)',\n        type: 'volatility',\n        status: 'neutral',\n        value: 1250,\n        strength: 60,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Keltner',\n        name: 'Keltner Channel',\n        type: 'volatility',\n        status: 'bullish',\n        value: 'Above Mid',\n        strength: 75,\n        lastUpdate: new Date()\n    },\n    // Volume Indicators\n    {\n        id: 'Volume',\n        name: 'Volume',\n        type: 'volume',\n        status: 'bullish',\n        value: '125% Avg',\n        strength: 80,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'VWAP',\n        name: 'VWAP',\n        type: 'volume',\n        status: 'bullish',\n        value: 51900,\n        strength: 73,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'MFI',\n        name: 'MFI (14)',\n        type: 'volume',\n        status: 'neutral',\n        value: 58.2,\n        strength: 48,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'OBV',\n        name: 'On Balance Volume',\n        type: 'volume',\n        status: 'bullish',\n        value: 'Rising',\n        strength: 77,\n        lastUpdate: new Date()\n    },\n    // Support/Resistance\n    {\n        id: 'Support1',\n        name: 'Support Level 1',\n        type: 'support_resistance',\n        status: 'bullish',\n        value: 50800,\n        strength: 85,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Resistance1',\n        name: 'Resistance Level 1',\n        type: 'support_resistance',\n        status: 'neutral',\n        value: 54200,\n        strength: 60,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'Pivot',\n        name: 'Pivot Point',\n        type: 'support_resistance',\n        status: 'bullish',\n        value: 52100,\n        strength: 70,\n        lastUpdate: new Date()\n    },\n    // Macro Indicators\n    {\n        id: 'SP500',\n        name: 'S&P 500',\n        type: 'macro',\n        status: 'bullish',\n        value: '+0.8%',\n        strength: 65,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'DXY',\n        name: 'DXY Index',\n        type: 'macro',\n        status: 'bearish',\n        value: 104.2,\n        strength: 58,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'VIX',\n        name: 'VIX',\n        type: 'macro',\n        status: 'bullish',\n        value: 18.3,\n        strength: 72,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'BTCD',\n        name: 'BTC Dominance',\n        type: 'macro',\n        status: 'neutral',\n        value: '52.4%',\n        strength: 50,\n        lastUpdate: new Date()\n    },\n    // On-Chain\n    {\n        id: 'ExchangeFlow',\n        name: 'Exchange Flow',\n        type: 'on_chain',\n        status: 'bullish',\n        value: 'Outflow',\n        strength: 78,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'WhaleActivity',\n        name: 'Whale Activity',\n        type: 'on_chain',\n        status: 'neutral',\n        value: 'Normal',\n        strength: 45,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'NetworkHealth',\n        name: 'Network Health',\n        type: 'on_chain',\n        status: 'bullish',\n        value: 'Strong',\n        strength: 82,\n        lastUpdate: new Date()\n    },\n    // Sentiment\n    {\n        id: 'FearGreed',\n        name: 'Fear & Greed',\n        type: 'sentiment',\n        status: 'bullish',\n        value: 65,\n        strength: 65,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'FundingRate',\n        name: 'Funding Rate',\n        type: 'sentiment',\n        status: 'neutral',\n        value: '0.01%',\n        strength: 40,\n        lastUpdate: new Date()\n    },\n    {\n        id: 'OpenInterest',\n        name: 'Open Interest',\n        type: 'sentiment',\n        status: 'bullish',\n        value: 'Rising',\n        strength: 70,\n        lastUpdate: new Date()\n    }\n];\nfunction IndicatorMatrix() {\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'bullish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 16\n                }, this);\n            case 'bearish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 16\n                }, this);\n            case 'neutral':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'bullish':\n                return 'bg-green-400/20 border-green-400/30';\n            case 'bearish':\n                return 'bg-red-400/20 border-red-400/30';\n            case 'neutral':\n                return 'bg-yellow-400/20 border-yellow-400/30';\n            default:\n                return 'bg-gray-400/20 border-gray-400/30';\n        }\n    };\n    const getStrengthColor = (strength)=>{\n        if (strength >= 80) return 'bg-green-400';\n        if (strength >= 60) return 'bg-yellow-400';\n        if (strength >= 40) return 'bg-orange-400';\n        return 'bg-red-400';\n    };\n    const filteredIndicators = mockIndicators.filter((indicator)=>{\n        const matchesType = selectedType === 'all' || indicator.type === selectedType;\n        const matchesSearch = indicator.name.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    const indicatorTypes = [\n        {\n            value: 'all',\n            label: 'All'\n        },\n        {\n            value: 'momentum',\n            label: 'Momentum'\n        },\n        {\n            value: 'trend',\n            label: 'Trend'\n        },\n        {\n            value: 'volatility',\n            label: 'Volatility'\n        },\n        {\n            value: 'volume',\n            label: 'Volume'\n        },\n        {\n            value: 'support_resistance',\n            label: 'S/R'\n        },\n        {\n            value: 'macro',\n            label: 'Macro'\n        },\n        {\n            value: 'on_chain',\n            label: 'On-Chain'\n        },\n        {\n            value: 'sentiment',\n            label: 'Sentiment'\n        }\n    ];\n    const getTypeStats = ()=>{\n        const stats = {\n            bullish: filteredIndicators.filter((i)=>i.status === 'bullish').length,\n            bearish: filteredIndicators.filter((i)=>i.status === 'bearish').length,\n            neutral: filteredIndicators.filter((i)=>i.status === 'neutral').length,\n            total: filteredIndicators.length\n        };\n        return stats;\n    };\n    const stats = getTypeStats();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"glass-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-xl font-bold text-white\",\n                                children: \"Technical Indicators Matrix\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                stats.bullish,\n                                                \" Bullish\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                stats.bearish,\n                                                \" Bearish\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                stats.neutral,\n                                                \" Neutral\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4 mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 text-white/60\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedType,\n                                        onChange: (e)=>setSelectedType(e.target.value),\n                                        className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-400\",\n                                        children: indicatorTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: type.value,\n                                                className: \"bg-slate-800\",\n                                                children: type.label\n                                            }, type.value, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 flex-1 max-w-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Filter_HelpCircle_Minus_Search_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 text-white/60\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search indicators...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-400 flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\",\n                        children: filteredIndicators.map((indicator)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-4 rounded-lg border transition-all duration-200 hover:scale-105 cursor-pointer ${getStatusColor(indicator.status)}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    getStatusIcon(indicator.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-white\",\n                                                        children: indicator.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"text-xs border-white/20 text-white/70\",\n                                                children: indicator.type\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: typeof indicator.value === 'number' ? indicator.value.toLocaleString() : indicator.value\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-white/60\",\n                                                        children: \"Strength:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 h-2 bg-white/20 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `h-2 rounded-full transition-all duration-300 ${getStrengthColor(indicator.strength)}`,\n                                                            style: {\n                                                                width: `${indicator.strength}%`\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-white font-medium\",\n                                                        children: [\n                                                            indicator.strength,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, indicator.id, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    filteredIndicators.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/60\",\n                            children: \"No indicators found matching your criteria.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/indicators/IndicatorMatrix.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9pbmRpY2F0b3JzL0luZGljYXRvck1hdHJpeC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFdUM7QUFDd0M7QUFDbEM7QUFReEI7QUFhckIsdUVBQXVFO0FBQ3ZFLE1BQU1hLGlCQUFrQztJQUN0QyxzQkFBc0I7SUFDdEI7UUFBRUMsSUFBSTtRQUFPQyxNQUFNO1FBQVlDLE1BQU07UUFBWUMsUUFBUTtRQUFXQyxPQUFPO1FBQU1DLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ3RIO1FBQUVQLElBQUk7UUFBUUMsTUFBTTtRQUFrQkMsTUFBTTtRQUFZQyxRQUFRO1FBQVdDLE9BQU87UUFBaUJDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ3hJO1FBQUVQLElBQUk7UUFBY0MsTUFBTTtRQUFpQkMsTUFBTTtRQUFZQyxRQUFRO1FBQVdDLE9BQU87UUFBTUMsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDbEk7UUFBRVAsSUFBSTtRQUFZQyxNQUFNO1FBQWVDLE1BQU07UUFBWUMsUUFBUTtRQUFXQyxPQUFPLENBQUM7UUFBTUMsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDL0g7UUFBRVAsSUFBSTtRQUFPQyxNQUFNO1FBQVlDLE1BQU07UUFBWUMsUUFBUTtRQUFXQyxPQUFPLENBQUM7UUFBT0MsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFFeEgsbUJBQW1CO0lBQ25CO1FBQUVQLElBQUk7UUFBU0MsTUFBTTtRQUFZQyxNQUFNO1FBQVNDLFFBQVE7UUFBV0MsT0FBTztRQUFPQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUN0SDtRQUFFUCxJQUFJO1FBQVNDLE1BQU07UUFBWUMsTUFBTTtRQUFTQyxRQUFRO1FBQVdDLE9BQU87UUFBT0MsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDdEg7UUFBRVAsSUFBSTtRQUFnQkMsTUFBTTtRQUFpQkMsTUFBTTtRQUFTQyxRQUFRO1FBQVdDLE9BQU87UUFBT0MsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDbEk7UUFBRVAsSUFBSTtRQUFZQyxNQUFNO1FBQWtCQyxNQUFNO1FBQVNDLFFBQVE7UUFBV0MsT0FBTztRQUFlQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUN2STtRQUFFUCxJQUFJO1FBQU9DLE1BQU07UUFBWUMsTUFBTTtRQUFTQyxRQUFRO1FBQVdDLE9BQU87UUFBTUMsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFFbkgsd0JBQXdCO0lBQ3hCO1FBQUVQLElBQUk7UUFBa0JDLE1BQU07UUFBbUJDLE1BQU07UUFBY0MsUUFBUTtRQUFXQyxPQUFPO1FBQVlDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ2hKO1FBQUVQLElBQUk7UUFBT0MsTUFBTTtRQUFZQyxNQUFNO1FBQWNDLFFBQVE7UUFBV0MsT0FBTztRQUFNQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUN4SDtRQUFFUCxJQUFJO1FBQVdDLE1BQU07UUFBbUJDLE1BQU07UUFBY0MsUUFBUTtRQUFXQyxPQUFPO1FBQWFDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBRTFJLG9CQUFvQjtJQUNwQjtRQUFFUCxJQUFJO1FBQVVDLE1BQU07UUFBVUMsTUFBTTtRQUFVQyxRQUFRO1FBQVdDLE9BQU87UUFBWUMsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDM0g7UUFBRVAsSUFBSTtRQUFRQyxNQUFNO1FBQVFDLE1BQU07UUFBVUMsUUFBUTtRQUFXQyxPQUFPO1FBQU9DLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ2xIO1FBQUVQLElBQUk7UUFBT0MsTUFBTTtRQUFZQyxNQUFNO1FBQVVDLFFBQVE7UUFBV0MsT0FBTztRQUFNQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUNwSDtRQUFFUCxJQUFJO1FBQU9DLE1BQU07UUFBcUJDLE1BQU07UUFBVUMsUUFBUTtRQUFXQyxPQUFPO1FBQVVDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBRWpJLHFCQUFxQjtJQUNyQjtRQUFFUCxJQUFJO1FBQVlDLE1BQU07UUFBbUJDLE1BQU07UUFBc0JDLFFBQVE7UUFBV0MsT0FBTztRQUFPQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUM3STtRQUFFUCxJQUFJO1FBQWVDLE1BQU07UUFBc0JDLE1BQU07UUFBc0JDLFFBQVE7UUFBV0MsT0FBTztRQUFPQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUNuSjtRQUFFUCxJQUFJO1FBQVNDLE1BQU07UUFBZUMsTUFBTTtRQUFzQkMsUUFBUTtRQUFXQyxPQUFPO1FBQU9DLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBRXRJLG1CQUFtQjtJQUNuQjtRQUFFUCxJQUFJO1FBQVNDLE1BQU07UUFBV0MsTUFBTTtRQUFTQyxRQUFRO1FBQVdDLE9BQU87UUFBU0MsVUFBVTtRQUFJQyxZQUFZLElBQUlDO0lBQU87SUFDdkg7UUFBRVAsSUFBSTtRQUFPQyxNQUFNO1FBQWFDLE1BQU07UUFBU0MsUUFBUTtRQUFXQyxPQUFPO1FBQU9DLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ3JIO1FBQUVQLElBQUk7UUFBT0MsTUFBTTtRQUFPQyxNQUFNO1FBQVNDLFFBQVE7UUFBV0MsT0FBTztRQUFNQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUM5RztRQUFFUCxJQUFJO1FBQVFDLE1BQU07UUFBaUJDLE1BQU07UUFBU0MsUUFBUTtRQUFXQyxPQUFPO1FBQVNDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBRTVILFdBQVc7SUFDWDtRQUFFUCxJQUFJO1FBQWdCQyxNQUFNO1FBQWlCQyxNQUFNO1FBQVlDLFFBQVE7UUFBV0MsT0FBTztRQUFXQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUN6STtRQUFFUCxJQUFJO1FBQWlCQyxNQUFNO1FBQWtCQyxNQUFNO1FBQVlDLFFBQVE7UUFBV0MsT0FBTztRQUFVQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUMxSTtRQUFFUCxJQUFJO1FBQWlCQyxNQUFNO1FBQWtCQyxNQUFNO1FBQVlDLFFBQVE7UUFBV0MsT0FBTztRQUFVQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUUxSSxZQUFZO0lBQ1o7UUFBRVAsSUFBSTtRQUFhQyxNQUFNO1FBQWdCQyxNQUFNO1FBQWFDLFFBQVE7UUFBV0MsT0FBTztRQUFJQyxVQUFVO1FBQUlDLFlBQVksSUFBSUM7SUFBTztJQUMvSDtRQUFFUCxJQUFJO1FBQWVDLE1BQU07UUFBZ0JDLE1BQU07UUFBYUMsUUFBUTtRQUFXQyxPQUFPO1FBQVNDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0lBQ3RJO1FBQUVQLElBQUk7UUFBZ0JDLE1BQU07UUFBaUJDLE1BQU07UUFBYUMsUUFBUTtRQUFXQyxPQUFPO1FBQVVDLFVBQVU7UUFBSUMsWUFBWSxJQUFJQztJQUFPO0NBQzFJO0FBRU0sU0FBU0M7SUFDZCxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHdkIsK0NBQVFBLENBQXdCO0lBQ3hFLE1BQU0sQ0FBQ3dCLFlBQVlDLGNBQWMsR0FBR3pCLCtDQUFRQSxDQUFDO0lBRTdDLE1BQU0wQixnQkFBZ0IsQ0FBQ1Y7UUFDckIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDVixrSUFBVUE7b0JBQUNxQixXQUFVOzs7Ozs7WUFDL0IsS0FBSztnQkFDSCxxQkFBTyw4REFBQ3BCLGtJQUFZQTtvQkFBQ29CLFdBQVU7Ozs7OztZQUNqQyxLQUFLO2dCQUNILHFCQUFPLDhEQUFDbkIsa0lBQUtBO29CQUFDbUIsV0FBVTs7Ozs7O1lBQzFCO2dCQUNFLHFCQUFPLDhEQUFDbEIsa0lBQVVBO29CQUFDa0IsV0FBVTs7Ozs7O1FBQ2pDO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQ1o7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLE1BQU1hLG1CQUFtQixDQUFDWDtRQUN4QixJQUFJQSxZQUFZLElBQUksT0FBTztRQUMzQixJQUFJQSxZQUFZLElBQUksT0FBTztRQUMzQixJQUFJQSxZQUFZLElBQUksT0FBTztRQUMzQixPQUFPO0lBQ1Q7SUFFQSxNQUFNWSxxQkFBcUJsQixlQUFlbUIsTUFBTSxDQUFDQyxDQUFBQTtRQUMvQyxNQUFNQyxjQUFjWCxpQkFBaUIsU0FBU1UsVUFBVWpCLElBQUksS0FBS087UUFDakUsTUFBTVksZ0JBQWdCRixVQUFVbEIsSUFBSSxDQUFDcUIsV0FBVyxHQUFHQyxRQUFRLENBQUNaLFdBQVdXLFdBQVc7UUFDbEYsT0FBT0YsZUFBZUM7SUFDeEI7SUFFQSxNQUFNRyxpQkFBb0U7UUFDeEU7WUFBRXBCLE9BQU87WUFBT3FCLE9BQU87UUFBTTtRQUM3QjtZQUFFckIsT0FBTztZQUFZcUIsT0FBTztRQUFXO1FBQ3ZDO1lBQUVyQixPQUFPO1lBQVNxQixPQUFPO1FBQVE7UUFDakM7WUFBRXJCLE9BQU87WUFBY3FCLE9BQU87UUFBYTtRQUMzQztZQUFFckIsT0FBTztZQUFVcUIsT0FBTztRQUFTO1FBQ25DO1lBQUVyQixPQUFPO1lBQXNCcUIsT0FBTztRQUFNO1FBQzVDO1lBQUVyQixPQUFPO1lBQVNxQixPQUFPO1FBQVE7UUFDakM7WUFBRXJCLE9BQU87WUFBWXFCLE9BQU87UUFBVztRQUN2QztZQUFFckIsT0FBTztZQUFhcUIsT0FBTztRQUFZO0tBQzFDO0lBRUQsTUFBTUMsZUFBZTtRQUNuQixNQUFNQyxRQUFRO1lBQ1pDLFNBQVNYLG1CQUFtQkMsTUFBTSxDQUFDVyxDQUFBQSxJQUFLQSxFQUFFMUIsTUFBTSxLQUFLLFdBQVcyQixNQUFNO1lBQ3RFQyxTQUFTZCxtQkFBbUJDLE1BQU0sQ0FBQ1csQ0FBQUEsSUFBS0EsRUFBRTFCLE1BQU0sS0FBSyxXQUFXMkIsTUFBTTtZQUN0RUUsU0FBU2YsbUJBQW1CQyxNQUFNLENBQUNXLENBQUFBLElBQUtBLEVBQUUxQixNQUFNLEtBQUssV0FBVzJCLE1BQU07WUFDdEVHLE9BQU9oQixtQkFBbUJhLE1BQU07UUFDbEM7UUFDQSxPQUFPSDtJQUNUO0lBRUEsTUFBTUEsUUFBUUQ7SUFFZCxxQkFDRSw4REFBQ3RDLHFEQUFJQTtRQUFDMEIsV0FBVTs7MEJBQ2QsOERBQUN4QiwyREFBVUE7O2tDQUNULDhEQUFDNEM7d0JBQUlwQixXQUFVOzswQ0FDYiw4REFBQ3ZCLDBEQUFTQTtnQ0FBQ3VCLFdBQVU7MENBQStCOzs7Ozs7MENBR3BELDhEQUFDb0I7Z0NBQUlwQixXQUFVOzBDQUViLDRFQUFDb0I7b0NBQUlwQixXQUFVOztzREFDYiw4REFBQ3RCLHVEQUFLQTs0Q0FBQzJDLFNBQVE7NENBQVVyQixXQUFVOztnREFDaENhLE1BQU1DLE9BQU87Z0RBQUM7Ozs7Ozs7c0RBRWpCLDhEQUFDcEMsdURBQUtBOzRDQUFDMkMsU0FBUTs0Q0FBU3JCLFdBQVU7O2dEQUMvQmEsTUFBTUksT0FBTztnREFBQzs7Ozs7OztzREFFakIsOERBQUN2Qyx1REFBS0E7NENBQUMyQyxTQUFROzRDQUFVckIsV0FBVTs7Z0RBQ2hDYSxNQUFNSyxPQUFPO2dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3ZCLDhEQUFDRTt3QkFBSXBCLFdBQVU7OzBDQUNiLDhEQUFDb0I7Z0NBQUlwQixXQUFVOztrREFDYiw4REFBQ2pCLGtJQUFNQTt3Q0FBQ2lCLFdBQVU7Ozs7OztrREFDbEIsOERBQUNzQjt3Q0FDQ2hDLE9BQU9LO3dDQUNQNEIsVUFBVSxDQUFDQyxJQUFNNUIsZ0JBQWdCNEIsRUFBRUMsTUFBTSxDQUFDbkMsS0FBSzt3Q0FDL0NVLFdBQVU7a0RBRVRVLGVBQWVnQixHQUFHLENBQUN0QyxDQUFBQSxxQkFDbEIsOERBQUN1QztnREFBd0JyQyxPQUFPRixLQUFLRSxLQUFLO2dEQUFFVSxXQUFVOzBEQUNuRFosS0FBS3VCLEtBQUs7K0NBREF2QixLQUFLRSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBDQU83Qiw4REFBQzhCO2dDQUFJcEIsV0FBVTs7a0RBQ2IsOERBQUNoQixrSUFBTUE7d0NBQUNnQixXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDNEI7d0NBQ0N4QyxNQUFLO3dDQUNMeUMsYUFBWTt3Q0FDWnZDLE9BQU9PO3dDQUNQMEIsVUFBVSxDQUFDQyxJQUFNMUIsY0FBYzBCLEVBQUVDLE1BQU0sQ0FBQ25DLEtBQUs7d0NBQzdDVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWxCLDhEQUFDekIsNERBQVdBOztrQ0FDViw4REFBQzZDO3dCQUFJcEIsV0FBVTtrQ0FDWkcsbUJBQW1CdUIsR0FBRyxDQUFDLENBQUNyQiwwQkFDdkIsOERBQUNlO2dDQUVDcEIsV0FBVyxDQUFDLGlGQUFpRixFQUFFQyxlQUFlSSxVQUFVaEIsTUFBTSxHQUFHOztrREFFakksOERBQUMrQjt3Q0FBSXBCLFdBQVU7OzBEQUNiLDhEQUFDb0I7Z0RBQUlwQixXQUFVOztvREFDWkQsY0FBY00sVUFBVWhCLE1BQU07a0VBQy9CLDhEQUFDeUM7d0RBQUs5QixXQUFVO2tFQUNiSyxVQUFVbEIsSUFBSTs7Ozs7Ozs7Ozs7OzBEQUduQiw4REFBQ1QsdURBQUtBO2dEQUFDMkMsU0FBUTtnREFBVXJCLFdBQVU7MERBQ2hDSyxVQUFVakIsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUluQiw4REFBQ2dDO3dDQUFJcEIsV0FBVTs7MERBQ2IsOERBQUNvQjtnREFBSXBCLFdBQVU7MERBQ1osT0FBT0ssVUFBVWYsS0FBSyxLQUFLLFdBQ3hCZSxVQUFVZixLQUFLLENBQUN5QyxjQUFjLEtBQzlCMUIsVUFBVWYsS0FBSzs7Ozs7OzBEQUlyQiw4REFBQzhCO2dEQUFJcEIsV0FBVTs7a0VBQ2IsOERBQUM4Qjt3REFBSzlCLFdBQVU7a0VBQXdCOzs7Ozs7a0VBQ3hDLDhEQUFDb0I7d0RBQUlwQixXQUFVO2tFQUNiLDRFQUFDb0I7NERBQ0NwQixXQUFXLENBQUMsNkNBQTZDLEVBQUVFLGlCQUFpQkcsVUFBVWQsUUFBUSxHQUFHOzREQUNqR3lDLE9BQU87Z0VBQUVDLE9BQU8sR0FBRzVCLFVBQVVkLFFBQVEsQ0FBQyxDQUFDLENBQUM7NERBQUM7Ozs7Ozs7Ozs7O2tFQUc3Qyw4REFBQ3VDO3dEQUFLOUIsV0FBVTs7NERBQ2JLLFVBQVVkLFFBQVE7NERBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWhDckJjLFVBQVVuQixFQUFFOzs7Ozs7Ozs7O29CQXdDdEJpQixtQkFBbUJhLE1BQU0sS0FBSyxtQkFDN0IsOERBQUNJO3dCQUFJcEIsV0FBVTtrQ0FDYiw0RUFBQ2tDOzRCQUFFbEMsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpDIiwic291cmNlcyI6WyIvVXNlcnMvYWxleGFuZHJlc2ltYXNtYWNpZWwvRG9jdW1lbnRzL2lhLXNpc3RlbWFzLzIwMjUtY3J5cHRvLXNpbmFpcy1haS9jcnlwdG8tc2lnbmFscy1haS9zcmMvY29tcG9uZW50cy9pbmRpY2F0b3JzL0luZGljYXRvck1hdHJpeC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJ1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnXG5pbXBvcnQgeyBcbiAgVHJlbmRpbmdVcCwgXG4gIFRyZW5kaW5nRG93biwgXG4gIE1pbnVzLCBcbiAgSGVscENpcmNsZSxcbiAgRmlsdGVyLFxuICBTZWFyY2hcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgSW5kaWNhdG9yU3RhdHVzLCBJbmRpY2F0b3JUeXBlIH0gZnJvbSAnQC90eXBlcy9pbmRpY2F0b3JzJ1xuXG5pbnRlcmZhY2UgSW5kaWNhdG9yRGF0YSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIHR5cGU6IEluZGljYXRvclR5cGVcbiAgc3RhdHVzOiBJbmRpY2F0b3JTdGF0dXNcbiAgdmFsdWU6IG51bWJlciB8IHN0cmluZ1xuICBzdHJlbmd0aDogbnVtYmVyXG4gIGxhc3RVcGRhdGU6IERhdGVcbn1cblxuLy8gTW9jayBpbmRpY2F0b3IgZGF0YSAtIEluIHJlYWwgYXBwLCB0aGlzIHdvdWxkIGNvbWUgZnJvbSBjYWxjdWxhdGlvbnNcbmNvbnN0IG1vY2tJbmRpY2F0b3JzOiBJbmRpY2F0b3JEYXRhW10gPSBbXG4gIC8vIE1vbWVudHVtIEluZGljYXRvcnNcbiAgeyBpZDogJ1JTSScsIG5hbWU6ICdSU0kgKDE0KScsIHR5cGU6ICdtb21lbnR1bScsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogMzUuMiwgc3RyZW5ndGg6IDc4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdNQUNEJywgbmFtZTogJ01BQ0QgKDEyLDI2LDkpJywgdHlwZTogJ21vbWVudHVtJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAnQnVsbGlzaCBDcm9zcycsIHN0cmVuZ3RoOiA4NSwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICB7IGlkOiAnU3RvY2hhc3RpYycsIG5hbWU6ICdTdG9jaGFzdGljICVLJywgdHlwZTogJ21vbWVudHVtJywgc3RhdHVzOiAnbmV1dHJhbCcsIHZhbHVlOiA1Mi4xLCBzdHJlbmd0aDogNDUsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ1dpbGxpYW1zJywgbmFtZTogJ1dpbGxpYW1zICVSJywgdHlwZTogJ21vbWVudHVtJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAtMjUuMywgc3RyZW5ndGg6IDcyLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdDQ0knLCBuYW1lOiAnQ0NJICgyMCknLCB0eXBlOiAnbW9tZW50dW0nLCBzdGF0dXM6ICdiZWFyaXNoJywgdmFsdWU6IC0xMjAuNSwgc3RyZW5ndGg6IDY4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIFxuICAvLyBUcmVuZCBJbmRpY2F0b3JzXG4gIHsgaWQ6ICdTTUEyMCcsIG5hbWU6ICdTTUEgKDIwKScsIHR5cGU6ICd0cmVuZCcsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogNTE4NTAsIHN0cmVuZ3RoOiA2NSwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICB7IGlkOiAnRU1BNTAnLCBuYW1lOiAnRU1BICg1MCknLCB0eXBlOiAndHJlbmQnLCBzdGF0dXM6ICdidWxsaXNoJywgdmFsdWU6IDUxMjAwLCBzdHJlbmd0aDogNzAsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ1BhcmFib2xpY1NBUicsIG5hbWU6ICdQYXJhYm9saWMgU0FSJywgdHlwZTogJ3RyZW5kJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiA1MDgwMCwgc3RyZW5ndGg6IDgyLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdJY2hpbW9rdScsIG5hbWU6ICdJY2hpbW9rdSBDbG91ZCcsIHR5cGU6ICd0cmVuZCcsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogJ0Fib3ZlIENsb3VkJywgc3RyZW5ndGg6IDg4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdBRFgnLCBuYW1lOiAnQURYICgxNCknLCB0eXBlOiAndHJlbmQnLCBzdGF0dXM6ICduZXV0cmFsJywgdmFsdWU6IDI4LjUsIHN0cmVuZ3RoOiA1NSwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICBcbiAgLy8gVm9sYXRpbGl0eSBJbmRpY2F0b3JzXG4gIHsgaWQ6ICdCb2xsaW5nZXJCYW5kcycsIG5hbWU6ICdCb2xsaW5nZXIgQmFuZHMnLCB0eXBlOiAndm9sYXRpbGl0eScsIHN0YXR1czogJ25ldXRyYWwnLCB2YWx1ZTogJ01pZC1CYW5kJywgc3RyZW5ndGg6IDQwLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdBVFInLCBuYW1lOiAnQVRSICgxNCknLCB0eXBlOiAndm9sYXRpbGl0eScsIHN0YXR1czogJ25ldXRyYWwnLCB2YWx1ZTogMTI1MCwgc3RyZW5ndGg6IDYwLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdLZWx0bmVyJywgbmFtZTogJ0tlbHRuZXIgQ2hhbm5lbCcsIHR5cGU6ICd2b2xhdGlsaXR5Jywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAnQWJvdmUgTWlkJywgc3RyZW5ndGg6IDc1LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIFxuICAvLyBWb2x1bWUgSW5kaWNhdG9yc1xuICB7IGlkOiAnVm9sdW1lJywgbmFtZTogJ1ZvbHVtZScsIHR5cGU6ICd2b2x1bWUnLCBzdGF0dXM6ICdidWxsaXNoJywgdmFsdWU6ICcxMjUlIEF2ZycsIHN0cmVuZ3RoOiA4MCwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICB7IGlkOiAnVldBUCcsIG5hbWU6ICdWV0FQJywgdHlwZTogJ3ZvbHVtZScsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogNTE5MDAsIHN0cmVuZ3RoOiA3MywgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICB7IGlkOiAnTUZJJywgbmFtZTogJ01GSSAoMTQpJywgdHlwZTogJ3ZvbHVtZScsIHN0YXR1czogJ25ldXRyYWwnLCB2YWx1ZTogNTguMiwgc3RyZW5ndGg6IDQ4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdPQlYnLCBuYW1lOiAnT24gQmFsYW5jZSBWb2x1bWUnLCB0eXBlOiAndm9sdW1lJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAnUmlzaW5nJywgc3RyZW5ndGg6IDc3LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIFxuICAvLyBTdXBwb3J0L1Jlc2lzdGFuY2VcbiAgeyBpZDogJ1N1cHBvcnQxJywgbmFtZTogJ1N1cHBvcnQgTGV2ZWwgMScsIHR5cGU6ICdzdXBwb3J0X3Jlc2lzdGFuY2UnLCBzdGF0dXM6ICdidWxsaXNoJywgdmFsdWU6IDUwODAwLCBzdHJlbmd0aDogODUsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ1Jlc2lzdGFuY2UxJywgbmFtZTogJ1Jlc2lzdGFuY2UgTGV2ZWwgMScsIHR5cGU6ICdzdXBwb3J0X3Jlc2lzdGFuY2UnLCBzdGF0dXM6ICduZXV0cmFsJywgdmFsdWU6IDU0MjAwLCBzdHJlbmd0aDogNjAsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ1Bpdm90JywgbmFtZTogJ1Bpdm90IFBvaW50JywgdHlwZTogJ3N1cHBvcnRfcmVzaXN0YW5jZScsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogNTIxMDAsIHN0cmVuZ3RoOiA3MCwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9LFxuICBcbiAgLy8gTWFjcm8gSW5kaWNhdG9yc1xuICB7IGlkOiAnU1A1MDAnLCBuYW1lOiAnUyZQIDUwMCcsIHR5cGU6ICdtYWNybycsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogJyswLjglJywgc3RyZW5ndGg6IDY1LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdEWFknLCBuYW1lOiAnRFhZIEluZGV4JywgdHlwZTogJ21hY3JvJywgc3RhdHVzOiAnYmVhcmlzaCcsIHZhbHVlOiAxMDQuMiwgc3RyZW5ndGg6IDU4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdWSVgnLCBuYW1lOiAnVklYJywgdHlwZTogJ21hY3JvJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAxOC4zLCBzdHJlbmd0aDogNzIsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ0JUQ0QnLCBuYW1lOiAnQlRDIERvbWluYW5jZScsIHR5cGU6ICdtYWNybycsIHN0YXR1czogJ25ldXRyYWwnLCB2YWx1ZTogJzUyLjQlJywgc3RyZW5ndGg6IDUwLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIFxuICAvLyBPbi1DaGFpblxuICB7IGlkOiAnRXhjaGFuZ2VGbG93JywgbmFtZTogJ0V4Y2hhbmdlIEZsb3cnLCB0eXBlOiAnb25fY2hhaW4nLCBzdGF0dXM6ICdidWxsaXNoJywgdmFsdWU6ICdPdXRmbG93Jywgc3RyZW5ndGg6IDc4LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdXaGFsZUFjdGl2aXR5JywgbmFtZTogJ1doYWxlIEFjdGl2aXR5JywgdHlwZTogJ29uX2NoYWluJywgc3RhdHVzOiAnbmV1dHJhbCcsIHZhbHVlOiAnTm9ybWFsJywgc3RyZW5ndGg6IDQ1LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdOZXR3b3JrSGVhbHRoJywgbmFtZTogJ05ldHdvcmsgSGVhbHRoJywgdHlwZTogJ29uX2NoYWluJywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiAnU3Ryb25nJywgc3RyZW5ndGg6IDgyLCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIFxuICAvLyBTZW50aW1lbnRcbiAgeyBpZDogJ0ZlYXJHcmVlZCcsIG5hbWU6ICdGZWFyICYgR3JlZWQnLCB0eXBlOiAnc2VudGltZW50Jywgc3RhdHVzOiAnYnVsbGlzaCcsIHZhbHVlOiA2NSwgc3RyZW5ndGg6IDY1LCBsYXN0VXBkYXRlOiBuZXcgRGF0ZSgpIH0sXG4gIHsgaWQ6ICdGdW5kaW5nUmF0ZScsIG5hbWU6ICdGdW5kaW5nIFJhdGUnLCB0eXBlOiAnc2VudGltZW50Jywgc3RhdHVzOiAnbmV1dHJhbCcsIHZhbHVlOiAnMC4wMSUnLCBzdHJlbmd0aDogNDAsIGxhc3RVcGRhdGU6IG5ldyBEYXRlKCkgfSxcbiAgeyBpZDogJ09wZW5JbnRlcmVzdCcsIG5hbWU6ICdPcGVuIEludGVyZXN0JywgdHlwZTogJ3NlbnRpbWVudCcsIHN0YXR1czogJ2J1bGxpc2gnLCB2YWx1ZTogJ1Jpc2luZycsIHN0cmVuZ3RoOiA3MCwgbGFzdFVwZGF0ZTogbmV3IERhdGUoKSB9XG5dXG5cbmV4cG9ydCBmdW5jdGlvbiBJbmRpY2F0b3JNYXRyaXgoKSB7XG4gIGNvbnN0IFtzZWxlY3RlZFR5cGUsIHNldFNlbGVjdGVkVHlwZV0gPSB1c2VTdGF0ZTxJbmRpY2F0b3JUeXBlIHwgJ2FsbCc+KCdhbGwnKVxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZSgnJylcblxuICBjb25zdCBnZXRTdGF0dXNJY29uID0gKHN0YXR1czogSW5kaWNhdG9yU3RhdHVzKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2J1bGxpc2gnOlxuICAgICAgICByZXR1cm4gPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICBjYXNlICdiZWFyaXNoJzpcbiAgICAgICAgcmV0dXJuIDxUcmVuZGluZ0Rvd24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC00MDBcIiAvPlxuICAgICAgY2FzZSAnbmV1dHJhbCc6XG4gICAgICAgIHJldHVybiA8TWludXMgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXllbGxvdy00MDBcIiAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxIZWxwQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBJbmRpY2F0b3JTdGF0dXMpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnYnVsbGlzaCc6XG4gICAgICAgIHJldHVybiAnYmctZ3JlZW4tNDAwLzIwIGJvcmRlci1ncmVlbi00MDAvMzAnXG4gICAgICBjYXNlICdiZWFyaXNoJzpcbiAgICAgICAgcmV0dXJuICdiZy1yZWQtNDAwLzIwIGJvcmRlci1yZWQtNDAwLzMwJ1xuICAgICAgY2FzZSAnbmV1dHJhbCc6XG4gICAgICAgIHJldHVybiAnYmcteWVsbG93LTQwMC8yMCBib3JkZXIteWVsbG93LTQwMC8zMCdcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnYmctZ3JheS00MDAvMjAgYm9yZGVyLWdyYXktNDAwLzMwJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFN0cmVuZ3RoQ29sb3IgPSAoc3RyZW5ndGg6IG51bWJlcikgPT4ge1xuICAgIGlmIChzdHJlbmd0aCA+PSA4MCkgcmV0dXJuICdiZy1ncmVlbi00MDAnXG4gICAgaWYgKHN0cmVuZ3RoID49IDYwKSByZXR1cm4gJ2JnLXllbGxvdy00MDAnXG4gICAgaWYgKHN0cmVuZ3RoID49IDQwKSByZXR1cm4gJ2JnLW9yYW5nZS00MDAnXG4gICAgcmV0dXJuICdiZy1yZWQtNDAwJ1xuICB9XG5cbiAgY29uc3QgZmlsdGVyZWRJbmRpY2F0b3JzID0gbW9ja0luZGljYXRvcnMuZmlsdGVyKGluZGljYXRvciA9PiB7XG4gICAgY29uc3QgbWF0Y2hlc1R5cGUgPSBzZWxlY3RlZFR5cGUgPT09ICdhbGwnIHx8IGluZGljYXRvci50eXBlID09PSBzZWxlY3RlZFR5cGVcbiAgICBjb25zdCBtYXRjaGVzU2VhcmNoID0gaW5kaWNhdG9yLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpXG4gICAgcmV0dXJuIG1hdGNoZXNUeXBlICYmIG1hdGNoZXNTZWFyY2hcbiAgfSlcblxuICBjb25zdCBpbmRpY2F0b3JUeXBlczogeyB2YWx1ZTogSW5kaWNhdG9yVHlwZSB8ICdhbGwnOyBsYWJlbDogc3RyaW5nIH1bXSA9IFtcbiAgICB7IHZhbHVlOiAnYWxsJywgbGFiZWw6ICdBbGwnIH0sXG4gICAgeyB2YWx1ZTogJ21vbWVudHVtJywgbGFiZWw6ICdNb21lbnR1bScgfSxcbiAgICB7IHZhbHVlOiAndHJlbmQnLCBsYWJlbDogJ1RyZW5kJyB9LFxuICAgIHsgdmFsdWU6ICd2b2xhdGlsaXR5JywgbGFiZWw6ICdWb2xhdGlsaXR5JyB9LFxuICAgIHsgdmFsdWU6ICd2b2x1bWUnLCBsYWJlbDogJ1ZvbHVtZScgfSxcbiAgICB7IHZhbHVlOiAnc3VwcG9ydF9yZXNpc3RhbmNlJywgbGFiZWw6ICdTL1InIH0sXG4gICAgeyB2YWx1ZTogJ21hY3JvJywgbGFiZWw6ICdNYWNybycgfSxcbiAgICB7IHZhbHVlOiAnb25fY2hhaW4nLCBsYWJlbDogJ09uLUNoYWluJyB9LFxuICAgIHsgdmFsdWU6ICdzZW50aW1lbnQnLCBsYWJlbDogJ1NlbnRpbWVudCcgfVxuICBdXG5cbiAgY29uc3QgZ2V0VHlwZVN0YXRzID0gKCkgPT4ge1xuICAgIGNvbnN0IHN0YXRzID0ge1xuICAgICAgYnVsbGlzaDogZmlsdGVyZWRJbmRpY2F0b3JzLmZpbHRlcihpID0+IGkuc3RhdHVzID09PSAnYnVsbGlzaCcpLmxlbmd0aCxcbiAgICAgIGJlYXJpc2g6IGZpbHRlcmVkSW5kaWNhdG9ycy5maWx0ZXIoaSA9PiBpLnN0YXR1cyA9PT0gJ2JlYXJpc2gnKS5sZW5ndGgsXG4gICAgICBuZXV0cmFsOiBmaWx0ZXJlZEluZGljYXRvcnMuZmlsdGVyKGkgPT4gaS5zdGF0dXMgPT09ICduZXV0cmFsJykubGVuZ3RoLFxuICAgICAgdG90YWw6IGZpbHRlcmVkSW5kaWNhdG9ycy5sZW5ndGhcbiAgICB9XG4gICAgcmV0dXJuIHN0YXRzXG4gIH1cblxuICBjb25zdCBzdGF0cyA9IGdldFR5cGVTdGF0cygpXG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZCBjbGFzc05hbWU9XCJnbGFzcy1jYXJkXCI+XG4gICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgIFRlY2huaWNhbCBJbmRpY2F0b3JzIE1hdHJpeFxuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICB7LyogU3RhdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic3VjY2Vzc1wiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICB7c3RhdHMuYnVsbGlzaH0gQnVsbGlzaFxuICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cImRhbmdlclwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICB7c3RhdHMuYmVhcmlzaH0gQmVhcmlzaFxuICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIndhcm5pbmdcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAge3N0YXRzLm5ldXRyYWx9IE5ldXRyYWxcbiAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiBGaWx0ZXJzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtdC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxGaWx0ZXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXdoaXRlLzYwXCIgLz5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkVHlwZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZFR5cGUoZS50YXJnZXQudmFsdWUgYXMgSW5kaWNhdG9yVHlwZSB8ICdhbGwnKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctd2hpdGUvMTAgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCByb3VuZGVkLWxnIHB4LTMgcHktMSB0ZXh0LXdoaXRlIHRleHQtc20gZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNDAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2luZGljYXRvclR5cGVzLm1hcCh0eXBlID0+IChcbiAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17dHlwZS52YWx1ZX0gdmFsdWU9e3R5cGUudmFsdWV9IGNsYXNzTmFtZT1cImJnLXNsYXRlLTgwMFwiPlxuICAgICAgICAgICAgICAgICAge3R5cGUubGFiZWx9XG4gICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgZmxleC0xIG1heC13LXhzXCI+XG4gICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC13aGl0ZS82MFwiIC8+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBpbmRpY2F0b3JzLi4uXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlLzEwIGJvcmRlciBib3JkZXItd2hpdGUvMjAgcm91bmRlZC1sZyBweC0zIHB5LTEgdGV4dC13aGl0ZSB0ZXh0LXNtIHBsYWNlaG9sZGVyLXdoaXRlLzQwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTQwMCBmbGV4LTFcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRIZWFkZXI+XG5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIHhsOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAge2ZpbHRlcmVkSW5kaWNhdG9ycy5tYXAoKGluZGljYXRvcikgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e2luZGljYXRvci5pZH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtbGcgYm9yZGVyIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMDUgY3Vyc29yLXBvaW50ZXIgJHtnZXRTdGF0dXNDb2xvcihpbmRpY2F0b3Iuc3RhdHVzKX1gfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNJY29uKGluZGljYXRvci5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgIHtpbmRpY2F0b3IubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJvcmRlci13aGl0ZS8yMCB0ZXh0LXdoaXRlLzcwXCI+XG4gICAgICAgICAgICAgICAgICB7aW5kaWNhdG9yLnR5cGV9XG4gICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAge3R5cGVvZiBpbmRpY2F0b3IudmFsdWUgPT09ICdudW1iZXInIFxuICAgICAgICAgICAgICAgICAgICA/IGluZGljYXRvci52YWx1ZS50b0xvY2FsZVN0cmluZygpIFxuICAgICAgICAgICAgICAgICAgICA6IGluZGljYXRvci52YWx1ZVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtd2hpdGUvNjBcIj5TdHJlbmd0aDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBoLTIgYmctd2hpdGUvMjAgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtnZXRTdHJlbmd0aENvbG9yKGluZGljYXRvci5zdHJlbmd0aCl9YH1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7aW5kaWNhdG9yLnN0cmVuZ3RofSVgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICB7aW5kaWNhdG9yLnN0cmVuZ3RofSVcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7ZmlsdGVyZWRJbmRpY2F0b3JzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS82MFwiPk5vIGluZGljYXRvcnMgZm91bmQgbWF0Y2hpbmcgeW91ciBjcml0ZXJpYS48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQmFkZ2UiLCJUcmVuZGluZ1VwIiwiVHJlbmRpbmdEb3duIiwiTWludXMiLCJIZWxwQ2lyY2xlIiwiRmlsdGVyIiwiU2VhcmNoIiwibW9ja0luZGljYXRvcnMiLCJpZCIsIm5hbWUiLCJ0eXBlIiwic3RhdHVzIiwidmFsdWUiLCJzdHJlbmd0aCIsImxhc3RVcGRhdGUiLCJEYXRlIiwiSW5kaWNhdG9yTWF0cml4Iiwic2VsZWN0ZWRUeXBlIiwic2V0U2VsZWN0ZWRUeXBlIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJnZXRTdGF0dXNJY29uIiwiY2xhc3NOYW1lIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdHJlbmd0aENvbG9yIiwiZmlsdGVyZWRJbmRpY2F0b3JzIiwiZmlsdGVyIiwiaW5kaWNhdG9yIiwibWF0Y2hlc1R5cGUiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImluZGljYXRvclR5cGVzIiwibGFiZWwiLCJnZXRUeXBlU3RhdHMiLCJzdGF0cyIsImJ1bGxpc2giLCJpIiwibGVuZ3RoIiwiYmVhcmlzaCIsIm5ldXRyYWwiLCJ0b3RhbCIsImRpdiIsInZhcmlhbnQiLCJzZWxlY3QiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJtYXAiLCJvcHRpb24iLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwic3BhbiIsInRvTG9jYWxlU3RyaW5nIiwic3R5bGUiLCJ3aWR0aCIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/indicators/IndicatorMatrix.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/signals/SignalCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/signals/SignalCard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignalCard: () => (/* binding */ SignalCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Clock,Shield,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(ssr)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ SignalCard auto */ \n\n\n\n\n\n\nfunction SignalCard({ signal, onClick }) {\n    const getSignalIcon = ()=>{\n        switch(signal.type){\n            case 'buy':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 16\n                }, this);\n            case 'sell':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-5 w-5 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 16\n                }, this);\n            case 'alert':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getSignalColor = ()=>{\n        switch(signal.type){\n            case 'buy':\n                return 'border-green-400/30 bg-green-400/5';\n            case 'sell':\n                return 'border-red-400/30 bg-red-400/5';\n            case 'alert':\n                return 'border-yellow-400/30 bg-yellow-400/5';\n            default:\n                return 'border-blue-400/30 bg-blue-400/5';\n        }\n    };\n    const getStrengthColor = ()=>{\n        switch(signal.strength){\n            case 'strong':\n                return 'success';\n            case 'moderate':\n                return 'warning';\n            case 'weak':\n                return 'secondary';\n            default:\n                return 'default';\n        }\n    };\n    const getPriorityColor = ()=>{\n        switch(signal.priority){\n            case 'critical':\n                return 'danger';\n            case 'high':\n                return 'warning';\n            case 'medium':\n                return 'default';\n            case 'low':\n                return 'secondary';\n            default:\n                return 'default';\n        }\n    };\n    const calculatePotentialReturn = ()=>{\n        if (!signal.entryPrice || !signal.targetPrice) return null;\n        const returnPercent = (signal.targetPrice - signal.entryPrice) / signal.entryPrice * 100;\n        return signal.type === 'sell' ? -returnPercent : returnPercent;\n    };\n    const potentialReturn = calculatePotentialReturn();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: `glass-card cursor-pointer transition-all duration-300 hover:scale-[1.02] ${getSignalColor()}`,\n        onClick: ()=>onClick?.(signal),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                getSignalIcon(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg font-bold text-white\",\n                                            children: signal.symbol\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/60\",\n                                            children: [\n                                                signal.timeframe,\n                                                \" • \",\n                                                (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(signal.timestamp, {\n                                                    addSuffix: true\n                                                })\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-end space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: getStrengthColor(),\n                                    children: signal.strength.toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: getPriorityColor(),\n                                    className: \"text-xs\",\n                                    children: signal.priority.toUpperCase()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Score:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-2 bg-white/20 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `h-2 rounded-full transition-all duration-300 ${signal.score >= 80 ? 'bg-green-400' : signal.score >= 60 ? 'bg-yellow-400' : 'bg-red-400'}`,\n                                                    style: {\n                                                        width: `${signal.score}%`\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: [\n                                                    signal.score,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Confidence:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white ml-1\",\n                                        children: [\n                                            signal.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    signal.entryPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 mb-1\",\n                                        children: \"Entry\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-white\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(signal.entryPrice)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            signal.targetPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-3 w-3 text-white/60\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60\",\n                                                children: \"Target\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-green-400\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(signal.targetPrice)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 15\n                            }, this),\n                            signal.stopLoss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-3 w-3 text-white/60\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/60\",\n                                                children: \"Stop\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium text-red-400\",\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(signal.stopLoss)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    potentialReturn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pt-2 border-t border-white/10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"Potential Return:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-sm font-medium ml-1 ${potentialReturn >= 0 ? 'text-green-400' : 'text-red-400'}`,\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(potentialReturn)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            signal.riskReward && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: \"R/R:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white ml-1\",\n                                        children: [\n                                            \"1:\",\n                                            signal.riskReward.toFixed(2)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-white/80 leading-relaxed\",\n                            children: signal.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-1 pt-2\",\n                        children: [\n                            signal.indicators.slice(0, 4).map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"text-xs border-white/20 text-white/70\",\n                                    children: indicator.name\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)),\n                            signal.indicators.length > 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                variant: \"outline\",\n                                className: \"text-xs border-white/20 text-white/70\",\n                                children: [\n                                    \"+\",\n                                    signal.indicators.length - 4,\n                                    \" more\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    signal.expiresAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 pt-2 text-xs text-white/60\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Clock_Shield_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-3 w-3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Expires \",\n                                    (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_9__.formatDistanceToNow)(signal.expiresAt, {\n                                        addSuffix: true\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/signals/SignalCard.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/signals/SignalCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            success: \"border-transparent bg-green-500 text-white shadow hover:bg-green-500/80\",\n            warning: \"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-500/80\",\n            danger: \"border-transparent bg-red-500 text-white shadow hover:bg-red-500/80\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/badge.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow backdrop-blur-sm bg-white/10 border-white/20\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   getColorByValue: () => (/* binding */ getColorByValue)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatNumber(num, decimals = 2) {\n    return new Intl.NumberFormat('en-US', {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    }).format(num);\n}\nfunction formatCurrency(num, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(num);\n}\nfunction formatPercentage(num, decimals = 2) {\n    return `${num >= 0 ? '+' : ''}${formatNumber(num, decimals)}%`;\n}\nfunction getColorByValue(value, thresholds) {\n    if (value >= thresholds.positive) return 'text-green-500';\n    if (value <= thresholds.negative) return 'text-red-500';\n    return 'text-yellow-500';\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2F2025-crypto-sinais-ai%2Fcrypto-signals-ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();