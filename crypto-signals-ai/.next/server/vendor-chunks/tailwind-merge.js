"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ })

};
;