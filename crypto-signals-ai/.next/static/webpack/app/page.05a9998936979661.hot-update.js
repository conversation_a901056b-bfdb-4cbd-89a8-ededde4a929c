"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const [signals, setSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockSignals);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockMarketData);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setLastUpdate(new Date());\n        setIsRefreshing(false);\n    };\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketData.marketCapChange24h > 0) score += 10;\n        if (marketData.fearGreedIndex > 50) score += 10;\n        if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = activeSignals.filter((s)=>s.type === 'buy');\n    const sellSignals = activeSignals.filter((s)=>s.type === 'sell');\n    const alertSignals = activeSignals.filter((s)=>s.type === 'alert');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {\n                    marketCap: marketData.totalMarketCap,\n                    marketCapChange: marketData.marketCapChange24h,\n                    volume24h: marketData.totalVolume24h,\n                    btcDominance: marketData.btcDominance,\n                    fearGreedIndex: marketData.fearGreedIndex,\n                    activeCoins: marketData.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"BGoGPXQR2qfMMdRe7n0zoTgGTAI=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9EYXNoYm9hcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVrRDtBQUNEO0FBQ0M7QUFDYTtBQUdnQjtBQUNsQztBQVN4QjtBQUlyQixxREFBcUQ7QUFDckQsTUFBTWUsaUJBQXFDO0lBQ3pDQyxnQkFBZ0I7SUFDaEJDLGdCQUFnQjtJQUNoQkMsY0FBYztJQUNkQyxnQkFBZ0I7SUFDaEJDLGFBQWE7SUFDYkMsb0JBQW9CO0lBQ3BCQyxhQUFhLElBQUlDO0FBQ25CO0FBRUEsTUFBTUMsY0FBd0I7SUFDNUI7UUFDRUMsSUFBSTtRQUNKQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsWUFBWTtRQUNaQyxXQUFXO1FBQ1hDLFdBQVcsSUFBSVYsS0FBS0EsS0FBS1csR0FBRyxLQUFLLElBQUksS0FBSztRQUMxQ0MsWUFBWTtRQUNaQyxhQUFhO1FBQ2JDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFdBQVc7WUFDVDtZQUNBO1lBQ0E7U0FDRDtRQUNEQyxZQUFZO1lBQ1Y7Z0JBQUVoQixJQUFJO2dCQUFPaUIsTUFBTTtnQkFBT2YsTUFBTTtnQkFBWWdCLE9BQU87Z0JBQUlDLFFBQVE7Z0JBQU1DLFFBQVE7Z0JBQVdDLGNBQWM7WUFBSztZQUMzRztnQkFBRXJCLElBQUk7Z0JBQVFpQixNQUFNO2dCQUFRZixNQUFNO2dCQUFZZ0IsT0FBTztvQkFBRUksTUFBTTtvQkFBS0MsUUFBUTtvQkFBS0MsV0FBVztnQkFBSTtnQkFBR0wsUUFBUTtnQkFBTUMsUUFBUTtnQkFBV0MsY0FBYztZQUFLO1lBQ3JKO2dCQUFFckIsSUFBSTtnQkFBVWlCLE1BQU07Z0JBQVVmLE1BQU07Z0JBQVVnQixPQUFPO2dCQUFTQyxRQUFRO2dCQUFNQyxRQUFRO2dCQUFXQyxjQUFjO1lBQUs7U0FDckg7UUFDREksWUFBWTtRQUNaTCxRQUFRO1FBQ1JNLE1BQU07WUFBQztZQUFPO1lBQWE7U0FBVztRQUN0Q0MsVUFBVTtJQUNaO0lBQ0E7UUFDRTNCLElBQUk7UUFDSkMsUUFBUTtRQUNSQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxXQUFXLElBQUlWLEtBQUtBLEtBQUtXLEdBQUcsS0FBSyxJQUFJLEtBQUs7UUFDMUNDLFlBQVk7UUFDWkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxXQUFXO1lBQ1Q7WUFDQTtTQUNEO1FBQ0RDLFlBQVk7WUFDVjtnQkFBRWhCLElBQUk7Z0JBQU9pQixNQUFNO2dCQUFPZixNQUFNO2dCQUFZZ0IsT0FBTztnQkFBSUMsUUFBUTtnQkFBTUMsUUFBUTtnQkFBV0MsY0FBYztZQUFLO1lBQzNHO2dCQUFFckIsSUFBSTtnQkFBZWlCLE1BQU07Z0JBQWdCZixNQUFNO2dCQUFhZ0IsT0FBTztnQkFBTUMsUUFBUTtnQkFBTUMsUUFBUTtnQkFBV0MsY0FBYztZQUFLO1NBQ2hJO1FBQ0RJLFlBQVk7UUFDWkwsUUFBUTtRQUNSTSxNQUFNO1lBQUM7WUFBUTtZQUFhO1NBQVU7UUFDdENDLFVBQVU7SUFDWjtJQUNBO1FBQ0UzQixJQUFJO1FBQ0pDLFFBQVE7UUFDUkMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxZQUFZO1FBQ1pDLFdBQVc7UUFDWEMsV0FBVyxJQUFJVixLQUFLQSxLQUFLVyxHQUFHLEtBQUssSUFBSSxLQUFLO1FBQzFDSSxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsV0FBVztZQUNUO1lBQ0E7U0FDRDtRQUNEQyxZQUFZO1lBQ1Y7Z0JBQUVoQixJQUFJO2dCQUFxQmlCLE1BQU07Z0JBQXNCZixNQUFNO2dCQUFzQmdCLE9BQU87Z0JBQU1DLFFBQVE7Z0JBQU1DLFFBQVE7Z0JBQVdDLGNBQWM7WUFBSztZQUNwSjtnQkFBRXJCLElBQUk7Z0JBQVVpQixNQUFNO2dCQUFVZixNQUFNO2dCQUFVZ0IsT0FBTztnQkFBU0MsUUFBUTtnQkFBTUMsUUFBUTtnQkFBV0MsY0FBYztZQUFLO1NBQ3JIO1FBQ0RELFFBQVE7UUFDUk0sTUFBTTtZQUFDO1lBQVk7WUFBUztTQUFTO1FBQ3JDQyxVQUFVO0lBQ1o7Q0FDRDtBQUVNLFNBQVNDOztJQUNkLE1BQU0sQ0FBQ0MsU0FBU0MsV0FBVyxHQUFHdEQsK0NBQVFBLENBQVd1QjtJQUNqRCxNQUFNLENBQUNnQyxZQUFZQyxjQUFjLEdBQUd4RCwrQ0FBUUEsQ0FBcUJjO0lBQ2pFLE1BQU0sQ0FBQzJDLFlBQVlDLGNBQWMsR0FBRzFELCtDQUFRQSxDQUFPLElBQUlzQjtJQUN2RCxNQUFNLENBQUNxQyxjQUFjQyxnQkFBZ0IsR0FBRzVELCtDQUFRQSxDQUFDO0lBRWpELE1BQU02RCxnQkFBZ0I7UUFDcEJELGdCQUFnQjtRQUNoQixvQkFBb0I7UUFDcEIsTUFBTSxJQUFJRSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1FBQ2pETCxjQUFjLElBQUlwQztRQUNsQnNDLGdCQUFnQjtJQUNsQjtJQUVBLE1BQU1LLG9CQUFvQixDQUFDbEI7UUFDekJtQixRQUFRQyxHQUFHLENBQUMsbUJBQW1CcEI7SUFDL0Isb0RBQW9EO0lBQ3REO0lBRUEsTUFBTXFCLHVCQUF1QjtRQUMzQiwwQ0FBMEM7UUFDMUMsSUFBSXZDLFFBQVEsR0FBRyxhQUFhOztRQUU1QixJQUFJMEIsV0FBV25DLGtCQUFrQixHQUFHLEdBQUdTLFNBQVM7UUFDaEQsSUFBSTBCLFdBQVdyQyxjQUFjLEdBQUcsSUFBSVcsU0FBUztRQUM3QyxJQUFJMEIsV0FBV3RDLFlBQVksR0FBRyxNQUFNc0MsV0FBV3RDLFlBQVksR0FBRyxJQUFJWSxTQUFTO1FBRTNFLE9BQU93QyxLQUFLQyxHQUFHLENBQUNELEtBQUtFLEdBQUcsQ0FBQzFDLE9BQU8sSUFBSTtJQUN0QztJQUVBLE1BQU0yQyxvQkFBb0JKO0lBQzFCLE1BQU1LLGtCQUFrQixDQUFDNUM7UUFDdkIsSUFBSUEsU0FBUyxJQUFJLE9BQU87WUFBRTZDLE9BQU87WUFBV0MsT0FBTztZQUFrQkMsSUFBSTtRQUFrQjtRQUMzRixJQUFJL0MsU0FBUyxJQUFJLE9BQU87WUFBRTZDLE9BQU87WUFBV0MsT0FBTztZQUFtQkMsSUFBSTtRQUFtQjtRQUM3RixPQUFPO1lBQUVGLE9BQU87WUFBV0MsT0FBTztZQUFnQkMsSUFBSTtRQUFnQjtJQUN4RTtJQUVBLE1BQU1DLGVBQWVKLGdCQUFnQkQ7SUFFckMsTUFBTU0sZ0JBQWdCekIsUUFBUTBCLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXBDLE1BQU0sS0FBSztJQUN2RCxNQUFNcUMsYUFBYUgsY0FBY0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFdEQsSUFBSSxLQUFLO0lBQ3hELE1BQU13RCxjQUFjSixjQUFjQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUV0RCxJQUFJLEtBQUs7SUFDekQsTUFBTXlELGVBQWVMLGNBQWNDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRXRELElBQUksS0FBSztJQUUxRCxxQkFDRSw4REFBQzBEO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ0U7b0NBQUdELFdBQVU7OENBQXFDOzs7Ozs7OENBR25ELDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBZ0I7Ozs7Ozs7Ozs7OztzQ0FJL0IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQ0NDLFNBQVM1QjtvQ0FDVDZCLFVBQVUvQjtvQ0FDVjBCLFdBQVU7O3NEQUVWLDhEQUFDMUUsa0pBQVNBOzRDQUFDMEUsV0FBVyxXQUE4QyxPQUFuQzFCLGVBQWUsaUJBQWlCOzs7Ozs7c0RBQ2pFLDhEQUFDZ0M7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFUiw4REFBQ0g7b0NBQU9ILFdBQVU7OENBQ2hCLDRFQUFDeEUsa0pBQUlBO3dDQUFDd0UsV0FBVTs7Ozs7Ozs7Ozs7OENBRWxCLDhEQUFDRztvQ0FBT0gsV0FBVTs4Q0FDaEIsNEVBQUN6RSxrSkFBUUE7d0NBQUN5RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNMUIsOERBQUNqRixxREFBSUE7b0JBQUNpRixXQUFVOzhCQUNkLDRFQUFDaEYsNERBQVdBO3dCQUFDZ0YsV0FBVTtrQ0FDckIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVyx3QkFBd0MsT0FBaEJSLGFBQWFELEVBQUU7c0RBQ3JELDRFQUFDZTtnREFBS04sV0FBVyxxQkFBd0MsT0FBbkJSLGFBQWFGLEtBQUs7O29EQUFJO29EQUMxQ0UsYUFBYUgsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUd0Qyw4REFBQ1U7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNNO2dEQUFLTixXQUFVOztvREFBc0JiO29EQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUc1RCw4REFBQ1k7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM3RSxtSkFBVUE7b0RBQUM2RSxXQUFVOzs7Ozs7OERBQ3RCLDhEQUFDTTs7d0RBQU1WLFdBQVdXLE1BQU07d0RBQUM7Ozs7Ozs7Ozs7Ozs7c0RBRTNCLDhEQUFDUjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM1RSxtSkFBWUE7b0RBQUM0RSxXQUFVOzs7Ozs7OERBQ3hCLDhEQUFDTTs7d0RBQU1ULFlBQVlVLE1BQU07d0RBQUM7Ozs7Ozs7Ozs7Ozs7c0RBRTVCLDhEQUFDUjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMzRSxtSkFBYUE7b0RBQUMyRSxXQUFVOzs7Ozs7OERBQ3pCLDhEQUFDTTs7d0RBQU1SLGFBQWFTLE1BQU07d0RBQUM7Ozs7Ozs7Ozs7Ozs7c0RBRTdCLDhEQUFDUjs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM5RSxtSkFBUUE7b0RBQUM4RSxXQUFVOzs7Ozs7OERBQ3BCLDhEQUFDTTs7d0RBQUs7d0RBQWNsQyxXQUFXb0Msa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRM0QsOERBQUM1RiwyREFBY0E7b0JBQ2I2RixXQUFXdkMsV0FBV3hDLGNBQWM7b0JBQ3BDZ0YsaUJBQWlCeEMsV0FBV25DLGtCQUFrQjtvQkFDOUM0RSxXQUFXekMsV0FBV3ZDLGNBQWM7b0JBQ3BDQyxjQUFjc0MsV0FBV3RDLFlBQVk7b0JBQ3JDQyxnQkFBZ0JxQyxXQUFXckMsY0FBYztvQkFDekNDLGFBQWFvQyxXQUFXcEMsV0FBVzs7Ozs7OzhCQUlyQyw4REFBQ2lFOztzQ0FDQyw4REFBQ0E7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDWTtvQ0FBR1osV0FBVTs4Q0FBZ0M7Ozs7Ozs4Q0FDOUMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQy9FLHVEQUFLQTs0Q0FBQzRGLFNBQVE7NENBQVViLFdBQVU7O2dEQUNoQ0osV0FBV1csTUFBTTtnREFBQzs7Ozs7OztzREFFckIsOERBQUN0Rix1REFBS0E7NENBQUM0RixTQUFROzRDQUFTYixXQUFVOztnREFDL0JILFlBQVlVLE1BQU07Z0RBQUM7Ozs7Ozs7c0RBRXRCLDhEQUFDdEYsdURBQUtBOzRDQUFDNEYsU0FBUTs0Q0FBVWIsV0FBVTs7Z0RBQ2hDRixhQUFhUyxNQUFNO2dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUszQiw4REFBQ1I7NEJBQUlDLFdBQVU7c0NBQ1pQLGNBQWNxQixHQUFHLENBQUMsQ0FBQ3BELHVCQUNsQiw4REFBQzdDLDJEQUFVQTtvQ0FFVDZDLFFBQVFBO29DQUNSMEMsU0FBU3hCO21DQUZKbEIsT0FBT3ZCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU3RCLDhEQUFDNEQ7O3NDQUNDLDhEQUFDYTs0QkFBR1osV0FBVTtzQ0FBcUM7Ozs7OztzQ0FDbkQsOERBQUNsRix3RUFBZUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLMUI7R0E3SmdCaUQ7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hbGV4YW5kcmVzaW1hc21hY2llbC9Eb2N1bWVudHMvaWEtc2lzdGVtYXMvMjAyNS1jcnlwdG8tc2luYWlzLWFpL2NyeXB0by1zaWduYWxzLWFpL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9EYXNoYm9hcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgTWFya2V0T3ZlcnZpZXcgfSBmcm9tICcuL01hcmtldE92ZXJ2aWV3J1xuaW1wb3J0IHsgU2lnbmFsQ2FyZCB9IGZyb20gJy4uL3NpZ25hbHMvU2lnbmFsQ2FyZCdcbmltcG9ydCB7IEluZGljYXRvck1hdHJpeCB9IGZyb20gJy4uL2luZGljYXRvcnMvSW5kaWNhdG9yTWF0cml4J1xuaW1wb3J0IHsgTWFya2V0SGVhdG1hcCB9IGZyb20gJy4uL2NoYXJ0cy9NYXJrZXRIZWF0bWFwJ1xuaW1wb3J0IHsgQ3J5cHRvTWV0cmljcyB9IGZyb20gJy4vQ3J5cHRvTWV0cmljcydcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgXG4gIEFjdGl2aXR5LCBcbiAgVHJlbmRpbmdVcCwgXG4gIFRyZW5kaW5nRG93biwgXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIFJlZnJlc2hDdyxcbiAgU2V0dGluZ3MsXG4gIEJlbGxcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgU2lnbmFsIH0gZnJvbSAnQC90eXBlcy9zaWduYWxzJ1xuaW1wb3J0IHsgTWFya2V0T3ZlcnZpZXcgYXMgTWFya2V0T3ZlcnZpZXdUeXBlIH0gZnJvbSAnQC90eXBlcy9tYXJrZXQnXG5cbi8vIE1vY2sgZGF0YSAtIEluIHJlYWwgYXBwLCB0aGlzIHdvdWxkIGNvbWUgZnJvbSBBUElzXG5jb25zdCBtb2NrTWFya2V0RGF0YTogTWFya2V0T3ZlcnZpZXdUeXBlID0ge1xuICB0b3RhbE1hcmtldENhcDogMjM0MDAwMDAwMDAwMCxcbiAgdG90YWxWb2x1bWUyNGg6IDg5MDAwMDAwMDAwLFxuICBidGNEb21pbmFuY2U6IDUyLjQsXG4gIGZlYXJHcmVlZEluZGV4OiA2NSxcbiAgYWN0aXZlQ29pbnM6IDI4NDcsXG4gIG1hcmtldENhcENoYW5nZTI0aDogMi4zLFxuICBsYXN0VXBkYXRlZDogbmV3IERhdGUoKVxufVxuXG5jb25zdCBtb2NrU2lnbmFsczogU2lnbmFsW10gPSBbXG4gIHtcbiAgICBpZDogJzEnLFxuICAgIHN5bWJvbDogJ0JUQy9VU0RUJyxcbiAgICB0eXBlOiAnYnV5JyxcbiAgICBzdHJlbmd0aDogJ3N0cm9uZycsXG4gICAgcHJpb3JpdHk6ICdoaWdoJyxcbiAgICBzY29yZTogODcsXG4gICAgY29uZmlkZW5jZTogOTIsXG4gICAgdGltZWZyYW1lOiAnNGgnLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDIgKiA2MCAqIDEwMDApLFxuICAgIGVudHJ5UHJpY2U6IDUyMzUwLFxuICAgIHRhcmdldFByaWNlOiA1NDUwMCxcbiAgICBzdG9wTG9zczogNTA4MDAsXG4gICAgdGl0bGU6ICdTdHJvbmcgQnV5IFNpZ25hbCAtIEJUQy9VU0RUJyxcbiAgICBkZXNjcmlwdGlvbjogJ011bHRpcGxlIGluZGljYXRvcnMgY29uZmlybSBidWxsaXNoIG1vbWVudHVtIHdpdGggUlNJIG92ZXJzb2xkIHJlY292ZXJ5IGFuZCBNQUNEIGJ1bGxpc2ggY3Jvc3NvdmVyLicsXG4gICAgcmVhc29uaW5nOiBbXG4gICAgICAnUlNJIHNob3dzIGJ1bGxpc2ggc2lnbmFsIHdpdGggODUuMiUgc3RyZW5ndGgnLFxuICAgICAgJ01BQ0Qgc2hvd3MgYnVsbGlzaCBzaWduYWwgd2l0aCA3OC45JSBzdHJlbmd0aCcsXG4gICAgICAnVm9sdW1lIHNob3dzIGJ1bGxpc2ggc2lnbmFsIHdpdGggNzIuMSUgc3RyZW5ndGgnXG4gICAgXSxcbiAgICBpbmRpY2F0b3JzOiBbXG4gICAgICB7IGlkOiAnUlNJJywgbmFtZTogJ1JTSScsIHR5cGU6ICdtb21lbnR1bScsIHZhbHVlOiAzNSwgd2VpZ2h0OiAwLjE1LCBzdGF0dXM6ICdidWxsaXNoJywgY29uZmlybWF0aW9uOiB0cnVlIH0sXG4gICAgICB7IGlkOiAnTUFDRCcsIG5hbWU6ICdNQUNEJywgdHlwZTogJ21vbWVudHVtJywgdmFsdWU6IHsgbWFjZDogMC41LCBzaWduYWw6IDAuMywgaGlzdG9ncmFtOiAwLjIgfSwgd2VpZ2h0OiAwLjIwLCBzdGF0dXM6ICdidWxsaXNoJywgY29uZmlybWF0aW9uOiB0cnVlIH0sXG4gICAgICB7IGlkOiAnVm9sdW1lJywgbmFtZTogJ1ZvbHVtZScsIHR5cGU6ICd2b2x1bWUnLCB2YWx1ZTogMTI1MDAwMCwgd2VpZ2h0OiAwLjA4LCBzdGF0dXM6ICdidWxsaXNoJywgY29uZmlybWF0aW9uOiB0cnVlIH1cbiAgICBdLFxuICAgIHJpc2tSZXdhcmQ6IDIuMixcbiAgICBzdGF0dXM6ICdhY3RpdmUnLFxuICAgIHRhZ3M6IFsnYnV5JywgJ3RlY2huaWNhbCcsICdtb21lbnR1bSddLFxuICAgIGNhdGVnb3J5OiAndGVjaG5pY2FsX2FuYWx5c2lzJ1xuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICBzeW1ib2w6ICdFVEgvVVNEVCcsXG4gICAgdHlwZTogJ3NlbGwnLFxuICAgIHN0cmVuZ3RoOiAnbW9kZXJhdGUnLFxuICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICBzY29yZTogNzIsXG4gICAgY29uZmlkZW5jZTogNzgsXG4gICAgdGltZWZyYW1lOiAnMWgnLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDUgKiA2MCAqIDEwMDApLFxuICAgIGVudHJ5UHJpY2U6IDMxODAsXG4gICAgdGFyZ2V0UHJpY2U6IDMwNTAsXG4gICAgc3RvcExvc3M6IDMyNTAsXG4gICAgdGl0bGU6ICdNb2RlcmF0ZSBTZWxsIFNpZ25hbCAtIEVUSC9VU0RUJyxcbiAgICBkZXNjcmlwdGlvbjogJ092ZXJib3VnaHQgY29uZGl0aW9ucyB3aXRoIGhpZ2ggZnVuZGluZyByYXRlcyBzdWdnZXN0IHBvdGVudGlhbCBjb3JyZWN0aW9uLicsXG4gICAgcmVhc29uaW5nOiBbXG4gICAgICAnUlNJIHNob3dzIGJlYXJpc2ggc2lnbmFsIHdpdGggNzUuNCUgc3RyZW5ndGgnLFxuICAgICAgJ0Z1bmRpbmcgcmF0ZSBhdCAwLjA4JSBpbmRpY2F0ZXMgb3ZlcmxldmVyYWdlZCBsb25ncydcbiAgICBdLFxuICAgIGluZGljYXRvcnM6IFtcbiAgICAgIHsgaWQ6ICdSU0knLCBuYW1lOiAnUlNJJywgdHlwZTogJ21vbWVudHVtJywgdmFsdWU6IDc4LCB3ZWlnaHQ6IDAuMTUsIHN0YXR1czogJ2JlYXJpc2gnLCBjb25maXJtYXRpb246IHRydWUgfSxcbiAgICAgIHsgaWQ6ICdGdW5kaW5nUmF0ZScsIG5hbWU6ICdGdW5kaW5nIFJhdGUnLCB0eXBlOiAnc2VudGltZW50JywgdmFsdWU6IDAuMDgsIHdlaWdodDogMC4xMCwgc3RhdHVzOiAnYmVhcmlzaCcsIGNvbmZpcm1hdGlvbjogdHJ1ZSB9XG4gICAgXSxcbiAgICByaXNrUmV3YXJkOiAxLjgsXG4gICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICB0YWdzOiBbJ3NlbGwnLCAndGVjaG5pY2FsJywgJ2Z1bmRpbmcnXSxcbiAgICBjYXRlZ29yeTogJ3RlY2huaWNhbF9hbmFseXNpcydcbiAgfSxcbiAge1xuICAgIGlkOiAnMycsXG4gICAgc3ltYm9sOiAnU09ML1VTRFQnLFxuICAgIHR5cGU6ICdhbGVydCcsXG4gICAgc3RyZW5ndGg6ICdzdHJvbmcnLFxuICAgIHByaW9yaXR5OiAnaGlnaCcsXG4gICAgc2NvcmU6IDg1LFxuICAgIGNvbmZpZGVuY2U6IDkwLFxuICAgIHRpbWVmcmFtZTogJzE1bScsXG4gICAgdGltZXN0YW1wOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMSAqIDYwICogMTAwMCksXG4gICAgdGl0bGU6ICdCcmVha291dCBBbGVydCAtIFNPTC9VU0RUJyxcbiAgICBkZXNjcmlwdGlvbjogJ1ByaWNlIGhhcyBicm9rZW4gYWJvdmUga2V5IHJlc2lzdGFuY2UgbGV2ZWwgd2l0aCBoaWdoIHZvbHVtZSBjb25maXJtYXRpb24uJyxcbiAgICByZWFzb25pbmc6IFtcbiAgICAgICdQcmljZSBicm9rZSByZXNpc3RhbmNlIGF0ICQ5OC41MCcsXG4gICAgICAnVm9sdW1lIHNwaWtlIG9mIDM0MCUgY29uZmlybXMgYnJlYWtvdXQnXG4gICAgXSxcbiAgICBpbmRpY2F0b3JzOiBbXG4gICAgICB7IGlkOiAnU3VwcG9ydFJlc2lzdGFuY2UnLCBuYW1lOiAnU3VwcG9ydC9SZXNpc3RhbmNlJywgdHlwZTogJ3N1cHBvcnRfcmVzaXN0YW5jZScsIHZhbHVlOiA5OC41LCB3ZWlnaHQ6IDAuMTIsIHN0YXR1czogJ2J1bGxpc2gnLCBjb25maXJtYXRpb246IHRydWUgfSxcbiAgICAgIHsgaWQ6ICdWb2x1bWUnLCBuYW1lOiAnVm9sdW1lJywgdHlwZTogJ3ZvbHVtZScsIHZhbHVlOiAyMTAwMDAwLCB3ZWlnaHQ6IDAuMDgsIHN0YXR1czogJ2J1bGxpc2gnLCBjb25maXJtYXRpb246IHRydWUgfVxuICAgIF0sXG4gICAgc3RhdHVzOiAnYWN0aXZlJyxcbiAgICB0YWdzOiBbJ2JyZWFrb3V0JywgJ2FsZXJ0JywgJ3ZvbHVtZSddLFxuICAgIGNhdGVnb3J5OiAncHJpY2VfYWN0aW9uJ1xuICB9XG5dXG5cbmV4cG9ydCBmdW5jdGlvbiBEYXNoYm9hcmQoKSB7XG4gIGNvbnN0IFtzaWduYWxzLCBzZXRTaWduYWxzXSA9IHVzZVN0YXRlPFNpZ25hbFtdPihtb2NrU2lnbmFscylcbiAgY29uc3QgW21hcmtldERhdGEsIHNldE1hcmtldERhdGFdID0gdXNlU3RhdGU8TWFya2V0T3ZlcnZpZXdUeXBlPihtb2NrTWFya2V0RGF0YSlcbiAgY29uc3QgW2xhc3RVcGRhdGUsIHNldExhc3RVcGRhdGVdID0gdXNlU3RhdGU8RGF0ZT4obmV3IERhdGUoKSlcbiAgY29uc3QgW2lzUmVmcmVzaGluZywgc2V0SXNSZWZyZXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IGhhbmRsZVJlZnJlc2ggPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNSZWZyZXNoaW5nKHRydWUpXG4gICAgLy8gU2ltdWxhdGUgQVBJIGNhbGxcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpXG4gICAgc2V0TGFzdFVwZGF0ZShuZXcgRGF0ZSgpKVxuICAgIHNldElzUmVmcmVzaGluZyhmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNpZ25hbENsaWNrID0gKHNpZ25hbDogU2lnbmFsKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1NpZ25hbCBjbGlja2VkOicsIHNpZ25hbClcbiAgICAvLyBJbiByZWFsIGFwcCwgdGhpcyB3b3VsZCBvcGVuIHNpZ25hbCBkZXRhaWxzIG1vZGFsXG4gIH1cblxuICBjb25zdCBnZXRNYXJrZXRIZWFsdGhTY29yZSA9ICgpID0+IHtcbiAgICAvLyBTaW1wbGUgY2FsY3VsYXRpb24gYmFzZWQgb24gbWFya2V0IGRhdGFcbiAgICBsZXQgc2NvcmUgPSA1MCAvLyBCYXNlIHNjb3JlXG4gICAgXG4gICAgaWYgKG1hcmtldERhdGEubWFya2V0Q2FwQ2hhbmdlMjRoID4gMCkgc2NvcmUgKz0gMTBcbiAgICBpZiAobWFya2V0RGF0YS5mZWFyR3JlZWRJbmRleCA+IDUwKSBzY29yZSArPSAxMFxuICAgIGlmIChtYXJrZXREYXRhLmJ0Y0RvbWluYW5jZSA+IDQ1ICYmIG1hcmtldERhdGEuYnRjRG9taW5hbmNlIDwgNjApIHNjb3JlICs9IDEwXG4gICAgXG4gICAgcmV0dXJuIE1hdGgubWluKE1hdGgubWF4KHNjb3JlLCAwKSwgMTAwKVxuICB9XG5cbiAgY29uc3QgbWFya2V0SGVhbHRoU2NvcmUgPSBnZXRNYXJrZXRIZWFsdGhTY29yZSgpXG4gIGNvbnN0IGdldEhlYWx0aFN0YXR1cyA9IChzY29yZTogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHNjb3JlID49IDcwKSByZXR1cm4geyBsYWJlbDogJ0JVTExJU0gnLCBjb2xvcjogJ3RleHQtZ3JlZW4tNDAwJywgYmc6ICdiZy1ncmVlbi00MDAvMjAnIH1cbiAgICBpZiAoc2NvcmUgPj0gNTApIHJldHVybiB7IGxhYmVsOiAnTkVVVFJBTCcsIGNvbG9yOiAndGV4dC15ZWxsb3ctNDAwJywgYmc6ICdiZy15ZWxsb3ctNDAwLzIwJyB9XG4gICAgcmV0dXJuIHsgbGFiZWw6ICdCRUFSSVNIJywgY29sb3I6ICd0ZXh0LXJlZC00MDAnLCBiZzogJ2JnLXJlZC00MDAvMjAnIH1cbiAgfVxuXG4gIGNvbnN0IGhlYWx0aFN0YXR1cyA9IGdldEhlYWx0aFN0YXR1cyhtYXJrZXRIZWFsdGhTY29yZSlcblxuICBjb25zdCBhY3RpdmVTaWduYWxzID0gc2lnbmFscy5maWx0ZXIocyA9PiBzLnN0YXR1cyA9PT0gJ2FjdGl2ZScpXG4gIGNvbnN0IGJ1eVNpZ25hbHMgPSBhY3RpdmVTaWduYWxzLmZpbHRlcihzID0+IHMudHlwZSA9PT0gJ2J1eScpXG4gIGNvbnN0IHNlbGxTaWduYWxzID0gYWN0aXZlU2lnbmFscy5maWx0ZXIocyA9PiBzLnR5cGUgPT09ICdzZWxsJylcbiAgY29uc3QgYWxlcnRTaWduYWxzID0gYWN0aXZlU2lnbmFscy5maWx0ZXIocyA9PiBzLnR5cGUgPT09ICdhbGVydCcpXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTkwMCB2aWEtcHVycGxlLTkwMCB0by1zbGF0ZS05MDAgcC02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHNwYWNlLXktOFwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPlxuICAgICAgICAgICAgICBDcnlwdG8gU2lnbmFscyBBSVxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNjBcIj5cbiAgICAgICAgICAgICAgQWR2YW5jZWQgdHJhZGluZyBzaWduYWxzIHBvd2VyZWQgYnkgMTAwKyB0ZWNobmljYWwgaW5kaWNhdG9yc1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJlZnJlc2h9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1JlZnJlc2hpbmd9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctd2hpdGUvMTAgaG92ZXI6Ymctd2hpdGUvMjAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXdoaXRlLzIwIHRleHQtd2hpdGUgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2BoLTQgdy00ICR7aXNSZWZyZXNoaW5nID8gJ2FuaW1hdGUtc3BpbicgOiAnJ31gfSAvPlxuICAgICAgICAgICAgICA8c3Bhbj5SZWZyZXNoPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMiBiZy13aGl0ZS8xMCBob3ZlcjpiZy13aGl0ZS8yMCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItd2hpdGUvMjAgdGV4dC13aGl0ZSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgPEJlbGwgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIGJnLXdoaXRlLzEwIGhvdmVyOmJnLXdoaXRlLzIwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci13aGl0ZS8yMCB0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1hcmtldCBIZWFsdGggSW5kaWNhdG9yICovfVxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJnbGFzcy1jYXJkXCI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHB4LTQgcHktMiByb3VuZGVkLWxnICR7aGVhbHRoU3RhdHVzLmJnfWB9PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgZm9udC1ib2xkIHRleHQtbGcgJHtoZWFsdGhTdGF0dXMuY29sb3J9YH0+XG4gICAgICAgICAgICAgICAgICAgIE1hcmtldCBIZWFsdGg6IHtoZWFsdGhTdGF0dXMubGFiZWx9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57bWFya2V0SGVhbHRoU2NvcmV9LzEwMDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC02IHRleHQtc20gdGV4dC13aGl0ZS82MFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8VHJlbmRpbmdVcCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPntidXlTaWduYWxzLmxlbmd0aH0gQnV5IFNpZ25hbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxUcmVuZGluZ0Rvd24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e3NlbGxTaWduYWxzLmxlbmd0aH0gU2VsbCBTaWduYWxzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQteWVsbG93LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj57YWxlcnRTaWduYWxzLmxlbmd0aH0gQWxlcnRzPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8QWN0aXZpdHkgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPkxhc3QgdXBkYXRlOiB7bGFzdFVwZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBNYXJrZXQgT3ZlcnZpZXcgKi99XG4gICAgICAgIDxNYXJrZXRPdmVydmlld1xuICAgICAgICAgIG1hcmtldENhcD17bWFya2V0RGF0YS50b3RhbE1hcmtldENhcH1cbiAgICAgICAgICBtYXJrZXRDYXBDaGFuZ2U9e21hcmtldERhdGEubWFya2V0Q2FwQ2hhbmdlMjRofVxuICAgICAgICAgIHZvbHVtZTI0aD17bWFya2V0RGF0YS50b3RhbFZvbHVtZTI0aH1cbiAgICAgICAgICBidGNEb21pbmFuY2U9e21hcmtldERhdGEuYnRjRG9taW5hbmNlfVxuICAgICAgICAgIGZlYXJHcmVlZEluZGV4PXttYXJrZXREYXRhLmZlYXJHcmVlZEluZGV4fVxuICAgICAgICAgIGFjdGl2ZUNvaW5zPXttYXJrZXREYXRhLmFjdGl2ZUNvaW5zfVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBBY3RpdmUgU2lnbmFscyAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5BY3RpdmUgU2lnbmFsczwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic3VjY2Vzc1wiIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7YnV5U2lnbmFscy5sZW5ndGh9IEJ1eVxuICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cImRhbmdlclwiIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7c2VsbFNpZ25hbHMubGVuZ3RofSBTZWxsXG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwid2FybmluZ1wiIGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICB7YWxlcnRTaWduYWxzLmxlbmd0aH0gQWxlcnRzXG4gICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgeGw6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgIHthY3RpdmVTaWduYWxzLm1hcCgoc2lnbmFsKSA9PiAoXG4gICAgICAgICAgICAgIDxTaWduYWxDYXJkXG4gICAgICAgICAgICAgICAga2V5PXtzaWduYWwuaWR9XG4gICAgICAgICAgICAgICAgc2lnbmFsPXtzaWduYWx9XG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2lnbmFsQ2xpY2t9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEluZGljYXRvciBNYXRyaXggKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5JbmRpY2F0b3IgTWF0cml4PC9oMj5cbiAgICAgICAgICA8SW5kaWNhdG9yTWF0cml4IC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiTWFya2V0T3ZlcnZpZXciLCJTaWduYWxDYXJkIiwiSW5kaWNhdG9yTWF0cml4IiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQmFkZ2UiLCJBY3Rpdml0eSIsIlRyZW5kaW5nVXAiLCJUcmVuZGluZ0Rvd24iLCJBbGVydFRyaWFuZ2xlIiwiUmVmcmVzaEN3IiwiU2V0dGluZ3MiLCJCZWxsIiwibW9ja01hcmtldERhdGEiLCJ0b3RhbE1hcmtldENhcCIsInRvdGFsVm9sdW1lMjRoIiwiYnRjRG9taW5hbmNlIiwiZmVhckdyZWVkSW5kZXgiLCJhY3RpdmVDb2lucyIsIm1hcmtldENhcENoYW5nZTI0aCIsImxhc3RVcGRhdGVkIiwiRGF0ZSIsIm1vY2tTaWduYWxzIiwiaWQiLCJzeW1ib2wiLCJ0eXBlIiwic3RyZW5ndGgiLCJwcmlvcml0eSIsInNjb3JlIiwiY29uZmlkZW5jZSIsInRpbWVmcmFtZSIsInRpbWVzdGFtcCIsIm5vdyIsImVudHJ5UHJpY2UiLCJ0YXJnZXRQcmljZSIsInN0b3BMb3NzIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInJlYXNvbmluZyIsImluZGljYXRvcnMiLCJuYW1lIiwidmFsdWUiLCJ3ZWlnaHQiLCJzdGF0dXMiLCJjb25maXJtYXRpb24iLCJtYWNkIiwic2lnbmFsIiwiaGlzdG9ncmFtIiwicmlza1Jld2FyZCIsInRhZ3MiLCJjYXRlZ29yeSIsIkRhc2hib2FyZCIsInNpZ25hbHMiLCJzZXRTaWduYWxzIiwibWFya2V0RGF0YSIsInNldE1hcmtldERhdGEiLCJsYXN0VXBkYXRlIiwic2V0TGFzdFVwZGF0ZSIsImlzUmVmcmVzaGluZyIsInNldElzUmVmcmVzaGluZyIsImhhbmRsZVJlZnJlc2giLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJoYW5kbGVTaWduYWxDbGljayIsImNvbnNvbGUiLCJsb2ciLCJnZXRNYXJrZXRIZWFsdGhTY29yZSIsIk1hdGgiLCJtaW4iLCJtYXgiLCJtYXJrZXRIZWFsdGhTY29yZSIsImdldEhlYWx0aFN0YXR1cyIsImxhYmVsIiwiY29sb3IiLCJiZyIsImhlYWx0aFN0YXR1cyIsImFjdGl2ZVNpZ25hbHMiLCJmaWx0ZXIiLCJzIiwiYnV5U2lnbmFscyIsInNlbGxTaWduYWxzIiwiYWxlcnRTaWduYWxzIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwic3BhbiIsImxlbmd0aCIsInRvTG9jYWxlVGltZVN0cmluZyIsIm1hcmtldENhcCIsIm1hcmtldENhcENoYW5nZSIsInZvbHVtZTI0aCIsImgyIiwidmFyaWFudCIsIm1hcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});