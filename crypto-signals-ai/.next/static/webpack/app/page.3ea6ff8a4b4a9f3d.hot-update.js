"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chart-column.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChartColumn)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n            key: \"c24i48\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17V9\",\n            key: \"2bz60n\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17V5\",\n            key: \"1frdt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17v-3\",\n            key: \"17ska0\"\n        }\n    ]\n];\nconst ChartColumn = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chart-column\", __iconNode);\n //# sourceMappingURL=chart-column.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/zap.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Zap)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.514.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z\",\n            key: \"1xq2db\"\n        }\n    ]\n];\nconst Zap = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"zap\", __iconNode);\n //# sourceMappingURL=zap.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx":
/*!*************************************************!*\
  !*** ./src/components/charts/MarketHeatmap.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MarketHeatmap: () => (/* binding */ MarketHeatmap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ MarketHeatmap auto */ \n\n\n\nconst mockHeatmapData = [\n    {\n        symbol: 'BTC',\n        name: 'Bitcoin',\n        price: 52350,\n        change24h: 2.3,\n        marketCap: 1020000000000,\n        volume24h: 28000000000\n    },\n    {\n        symbol: 'ETH',\n        name: 'Ethereum',\n        price: 3180,\n        change24h: 1.8,\n        marketCap: 382000000000,\n        volume24h: 15000000000\n    },\n    {\n        symbol: 'BNB',\n        name: 'BNB',\n        price: 315,\n        change24h: -0.5,\n        marketCap: 47000000000,\n        volume24h: 1200000000\n    },\n    {\n        symbol: 'SOL',\n        name: 'Solana',\n        price: 98.5,\n        change24h: 4.2,\n        marketCap: 45000000000,\n        volume24h: 2100000000\n    },\n    {\n        symbol: 'XRP',\n        name: 'XRP',\n        price: 0.52,\n        change24h: -1.2,\n        marketCap: 29000000000,\n        volume24h: 1800000000\n    },\n    {\n        symbol: 'ADA',\n        name: 'Cardano',\n        price: 0.38,\n        change24h: 0.8,\n        marketCap: 13000000000,\n        volume24h: 450000000\n    },\n    {\n        symbol: 'AVAX',\n        name: 'Avalanche',\n        price: 28.5,\n        change24h: 3.1,\n        marketCap: 11000000000,\n        volume24h: 380000000\n    },\n    {\n        symbol: 'DOT',\n        name: 'Polkadot',\n        price: 6.2,\n        change24h: -0.8,\n        marketCap: 8500000000,\n        volume24h: 220000000\n    },\n    {\n        symbol: 'MATIC',\n        name: 'Polygon',\n        price: 0.85,\n        change24h: 2.1,\n        marketCap: 8200000000,\n        volume24h: 340000000\n    },\n    {\n        symbol: 'LINK',\n        name: 'Chainlink',\n        price: 14.2,\n        change24h: 1.5,\n        marketCap: 8800000000,\n        volume24h: 280000000\n    },\n    {\n        symbol: 'UNI',\n        name: 'Uniswap',\n        price: 8.9,\n        change24h: -2.1,\n        marketCap: 6700000000,\n        volume24h: 180000000\n    },\n    {\n        symbol: 'LTC',\n        name: 'Litecoin',\n        price: 92,\n        change24h: 0.3,\n        marketCap: 6900000000,\n        volume24h: 420000000\n    },\n    {\n        symbol: 'ATOM',\n        name: 'Cosmos',\n        price: 7.8,\n        change24h: 1.9,\n        marketCap: 3100000000,\n        volume24h: 95000000\n    },\n    {\n        symbol: 'FTM',\n        name: 'Fantom',\n        price: 0.42,\n        change24h: 5.2,\n        marketCap: 1200000000,\n        volume24h: 85000000\n    },\n    {\n        symbol: 'ALGO',\n        name: 'Algorand',\n        price: 0.18,\n        change24h: -1.5,\n        marketCap: 1400000000,\n        volume24h: 45000000\n    },\n    {\n        symbol: 'VET',\n        name: 'VeChain',\n        price: 0.025,\n        change24h: 0.9,\n        marketCap: 1800000000,\n        volume24h: 32000000\n    }\n];\nfunction MarketHeatmap() {\n    const getChangeColor = (change)=>{\n        if (change > 3) return 'bg-green-500';\n        if (change > 1) return 'bg-green-400';\n        if (change > 0) return 'bg-green-300';\n        if (change > -1) return 'bg-red-300';\n        if (change > -3) return 'bg-red-400';\n        return 'bg-red-500';\n    };\n    const getTextColor = (change)=>{\n        return Math.abs(change) > 1 ? 'text-white' : 'text-gray-800';\n    };\n    const getSize = (marketCap)=>{\n        // Normalize market cap to determine size\n        const maxCap = Math.max(...mockHeatmapData.map((d)=>d.marketCap));\n        const minCap = Math.min(...mockHeatmapData.map((d)=>d.marketCap));\n        const normalized = (marketCap - minCap) / (maxCap - minCap);\n        // Return size classes based on normalized value\n        if (normalized > 0.8) return 'col-span-4 row-span-3';\n        if (normalized > 0.6) return 'col-span-3 row-span-2';\n        if (normalized > 0.4) return 'col-span-2 row-span-2';\n        if (normalized > 0.2) return 'col-span-2 row-span-1';\n        return 'col-span-1 row-span-1';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"glass-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"Market Heatmap\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-white/60\",\n                        children: \"Size represents market cap, color represents 24h change\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-8 grid-rows-6 gap-2 h-96\",\n                        children: mockHeatmapData.map((crypto, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n                \".concat(getSize(crypto.marketCap), \"\\n                \").concat(getChangeColor(crypto.change24h), \"\\n                \").concat(getTextColor(crypto.change24h), \"\\n                rounded-lg p-3 flex flex-col justify-between\\n                transition-all duration-300 hover:scale-105 hover:z-10\\n                cursor-pointer relative group\\n              \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 whitespace-nowrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold\",\n                                                children: crypto.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Price: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.price)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Market Cap: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.marketCap)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Volume: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.volume24h)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"24h Change: \",\n                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPercentage)(crypto.change24h)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col h-full justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-bold text-sm mb-1\",\n                                                        children: crypto.symbol\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs opacity-80\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(crypto.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold text-sm\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatPercentage)(crypto.change24h)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, crypto.symbol, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex items-center justify-between text-sm text-white/60\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"24h Change:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-red-500 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"< -3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-red-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"-1% to -3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"0% to 1%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-400 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"1% to 3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 bg-green-500 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"> 3%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Size = Market Cap\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/charts/MarketHeatmap.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = MarketHeatmap;\nvar _c;\n$RefreshReg$(_c, \"MarketHeatmap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/CryptoMetrics.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CryptoMetrics: () => (/* binding */ CryptoMetrics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,DollarSign,TrendingDown,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ CryptoMetrics auto */ \n\n\n\n\n\nconst mockFundingRates = [\n    {\n        exchange: 'Binance',\n        symbol: 'BTCUSDT',\n        rate: 0.0125,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Bybit',\n        symbol: 'BTCUSDT',\n        rate: 0.0089,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'OKX',\n        symbol: 'BTCUSDT',\n        rate: 0.0156,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Binance',\n        symbol: 'ETHUSDT',\n        rate: 0.0078,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'Bybit',\n        symbol: 'ETHUSDT',\n        rate: 0.0092,\n        nextFunding: '4h 23m'\n    },\n    {\n        exchange: 'OKX',\n        symbol: 'ETHUSDT',\n        rate: 0.0134,\n        nextFunding: '4h 23m'\n    }\n];\nconst mockLiquidations = [\n    {\n        timeframe: '1h',\n        longs: 12500000,\n        shorts: 8200000,\n        total: 20700000\n    },\n    {\n        timeframe: '4h',\n        longs: 45000000,\n        shorts: 28000000,\n        total: 73000000\n    },\n    {\n        timeframe: '24h',\n        longs: 180000000,\n        shorts: 95000000,\n        total: 275000000\n    }\n];\nconst mockOnChainData = [\n    {\n        metric: 'Exchange Inflow',\n        value: '-2,450 BTC',\n        change24h: -15.2,\n        status: 'bullish'\n    },\n    {\n        metric: 'Exchange Outflow',\n        value: '+3,890 BTC',\n        change24h: 22.8,\n        status: 'bullish'\n    },\n    {\n        metric: 'Whale Transactions',\n        value: '1,247',\n        change24h: 8.5,\n        status: 'neutral'\n    },\n    {\n        metric: 'Active Addresses',\n        value: '985,432',\n        change24h: 3.2,\n        status: 'bullish'\n    },\n    {\n        metric: 'Network Hash Rate',\n        value: '450.2 EH/s',\n        change24h: 1.8,\n        status: 'bullish'\n    },\n    {\n        metric: 'Mining Difficulty',\n        value: '62.46 T',\n        change24h: 0.5,\n        status: 'neutral'\n    }\n];\nfunction CryptoMetrics() {\n    const getFundingRateColor = (rate)=>{\n        if (rate > 0.02) return 'text-red-400';\n        if (rate > 0.01) return 'text-yellow-400';\n        if (rate > 0) return 'text-green-400';\n        if (rate > -0.01) return 'text-blue-400';\n        return 'text-purple-400';\n    };\n    const getFundingRateStatus = (rate)=>{\n        if (rate > 0.02) return 'Extreme Greed';\n        if (rate > 0.01) return 'Greed';\n        if (rate > 0) return 'Bullish';\n        if (rate > -0.01) return 'Bearish';\n        return 'Extreme Fear';\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'bullish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, this);\n            case 'bearish':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const averageFundingBTC = mockFundingRates.filter((f)=>f.symbol === 'BTCUSDT').reduce((sum, f)=>sum + f.rate, 0) / 3;\n    const averageFundingETH = mockFundingRates.filter((f)=>f.symbol === 'ETHUSDT').reduce((sum, f)=>sum + f.rate, 0) / 3;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Funding Rates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    variant: \"outline\",\n                                    className: \"border-white/20 text-white/70\",\n                                    children: \"Live\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: \"BTC/USDT Average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold \".concat(getFundingRateColor(averageFundingBTC)),\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(averageFundingBTC * 100, 4)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: [\n                                            \"Status: \",\n                                            getFundingRateStatus(averageFundingBTC)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: \"ETH/USDT Average\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold \".concat(getFundingRateColor(averageFundingETH)),\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(averageFundingETH * 100, 4)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60\",\n                                        children: [\n                                            \"Status: \",\n                                            getFundingRateStatus(averageFundingETH)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-white/80\",\n                                        children: \"By Exchange\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    mockFundingRates.map((rate, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/60\",\n                                                            children: rate.exchange\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/80\",\n                                                            children: rate.symbol\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: getFundingRateColor(rate.rate),\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(rate.rate * 100, 4)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white/40 text-xs\",\n                                                            children: rate.nextFunding\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Liquidations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: mockLiquidations.map((liq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-white/5 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-white\",\n                                                children: liq.timeframe\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.total)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-400 font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.longs)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Longs\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-green-400 font-medium\",\n                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(liq.shorts)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white/60\",\n                                                        children: \"Shorts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex h-2 rounded-full overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-400\",\n                                                        style: {\n                                                            width: \"\".concat(liq.longs / liq.total * 100, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-400\",\n                                                        style: {\n                                                            width: \"\".concat(liq.shorts / liq.total * 100, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-xs text-white/60 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (liq.longs / liq.total * 100).toFixed(1),\n                                                            \"% Longs\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (liq.shorts / liq.total * 100).toFixed(1),\n                                                            \"% Shorts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"glass-card lg:col-span-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg font-bold text-white flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"On-Chain Metrics\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: mockOnChainData.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-white/5 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-white/60\",\n                                                    children: metric.metric\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                getStatusIcon(metric.status)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-white mb-1\",\n                                            children: metric.value\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                metric.change24h >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_DollarSign_TrendingDown_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm \".concat(metric.change24h >= 0 ? 'text-green-400' : 'text-red-400'),\n                                                    children: [\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatPercentage)(metric.change24h),\n                                                        \" 24h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/CryptoMetrics.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_c = CryptoMetrics;\nvar _c;\n$RefreshReg$(_c, \"CryptoMetrics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CryptoMetrics */ \"(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const [signals, setSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockSignals);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockMarketData);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setLastUpdate(new Date());\n        setIsRefreshing(false);\n    };\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketData.marketCapChange24h > 0) score += 10;\n        if (marketData.fearGreedIndex > 50) score += 10;\n        if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = activeSignals.filter((s)=>s.type === 'buy');\n    const sellSignals = activeSignals.filter((s)=>s.type === 'sell');\n    const alertSignals = activeSignals.filter((s)=>s.type === 'alert');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {\n                    marketCap: marketData.totalMarketCap,\n                    marketCapChange: marketData.marketCapChange24h,\n                    volume24h: marketData.totalVolume24h,\n                    btcDominance: marketData.btcDominance,\n                    fearGreedIndex: marketData.fearGreedIndex,\n                    activeCoins: marketData.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"BGoGPXQR2qfMMdRe7n0zoTgGTAI=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});