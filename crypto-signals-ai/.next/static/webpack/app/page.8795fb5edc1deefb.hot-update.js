"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMarketData */ \"(app-pages-browser)/./src/hooks/useMarketData.ts\");\n/* harmony import */ var _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSignals */ \"(app-pages-browser)/./src/hooks/useSignals.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./src/hooks/useWebSocket.ts\");\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CryptoMetrics */ \"(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const { marketOverview, isLoading: marketLoading, refreshData } = (0,_hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData)();\n    const { filteredSignals: signals, isLoading: signalsLoading, refreshSignals, getSignalsByType } = (0,_hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals)();\n    const { isConnected, lastMessage, connectionStatus } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket)('mock://localhost');\n    const { showSignalNotification, requestPermission } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        try {\n            await Promise.all([\n                refreshData(),\n                refreshSignals()\n            ]);\n            setLastUpdate(new Date());\n        } catch (error) {\n            console.error('Failed to refresh data:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === 'signal_alert') {\n                showSignalNotification(lastMessage.data);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        lastMessage,\n        showSignalNotification\n    ]);\n    // Request notification permission on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            requestPermission();\n        }\n    }[\"Dashboard.useEffect\"], [\n        requestPermission\n    ]);\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketData.marketCapChange24h > 0) score += 10;\n        if (marketData.fearGreedIndex > 50) score += 10;\n        if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = activeSignals.filter((s)=>s.type === 'buy');\n    const sellSignals = activeSignals.filter((s)=>s.type === 'sell');\n    const alertSignals = activeSignals.filter((s)=>s.type === 'alert');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_5__.MarketOverview, {\n                    marketCap: marketData.totalMarketCap,\n                    marketCapChange: marketData.marketCapChange24h,\n                    volume24h: marketData.totalVolume24h,\n                    btcDominance: marketData.btcDominance,\n                    fearGreedIndex: marketData.fearGreedIndex,\n                    activeCoins: marketData.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 192,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"/cBKIm9TCyA7/qejlneiUefSI44=\", false, function() {\n    return [\n        _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData,\n        _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});