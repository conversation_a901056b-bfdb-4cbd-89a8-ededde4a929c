"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMarketData */ \"(app-pages-browser)/./src/hooks/useMarketData.ts\");\n/* harmony import */ var _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSignals */ \"(app-pages-browser)/./src/hooks/useSignals.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./src/hooks/useWebSocket.ts\");\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CryptoMetrics */ \"(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_loading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/loading */ \"(app-pages-browser)/./src/components/ui/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const { marketOverview, isLoading: marketLoading, refreshData } = (0,_hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData)();\n    const { filteredSignals: signals, isLoading: signalsLoading, refreshSignals, getSignalsByType } = (0,_hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals)();\n    const { isConnected, lastMessage, connectionStatus } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket)('mock://localhost');\n    const { showSignalNotification, requestPermission } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        try {\n            await Promise.all([\n                refreshData(),\n                refreshSignals()\n            ]);\n            setLastUpdate(new Date());\n        } catch (error) {\n            console.error('Failed to refresh data:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === 'signal_alert') {\n                showSignalNotification(lastMessage.data);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        lastMessage,\n        showSignalNotification\n    ]);\n    // Request notification permission on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            requestPermission();\n        }\n    }[\"Dashboard.useEffect\"], [\n        requestPermission\n    ]);\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        if (!marketOverview) return 50;\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketOverview.marketCapChange24h > 0) score += 10;\n        if (marketOverview.fearGreedIndex > 50) score += 10;\n        if (marketOverview.btcDominance > 45 && marketOverview.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = getSignalsByType('buy').filter((s)=>s.status === 'active');\n    const sellSignals = getSignalsByType('sell').filter((s)=>s.status === 'active');\n    const alertSignals = getSignalsByType('alert').filter((s)=>s.status === 'active');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-400' : 'bg-red-400')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"WebSocket: \",\n                                                        connectionStatus\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                marketOverview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_5__.MarketOverview, {\n                    marketCap: marketOverview.totalMarketCap,\n                    marketCapChange: marketOverview.marketCapChange24h,\n                    volume24h: marketOverview.totalVolume24h,\n                    btcDominance: marketOverview.btcDominance,\n                    fearGreedIndex: marketOverview.fearGreedIndex,\n                    activeCoins: marketOverview.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading__WEBPACK_IMPORTED_MODULE_12__.LoadingState, {\n                            isLoading: signalsLoading,\n                            loadingComponent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                                children: Array.from({\n                                    length: 6\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_loading__WEBPACK_IMPORTED_MODULE_12__.LoadingCard, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 19\n                                    }, void 0))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                                children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__.SignalCard, {\n                                        signal: signal,\n                                        onClick: handleSignalClick\n                                    }, signal.id, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"/cBKIm9TCyA7/qejlneiUefSI44=\", false, function() {\n    return [\n        _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData,\n        _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/loading.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/loading.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingDots: () => (/* binding */ LoadingDots),\n/* harmony export */   LoadingOverlay: () => (/* binding */ LoadingOverlay),\n/* harmony export */   LoadingProgress: () => (/* binding */ LoadingProgress),\n/* harmony export */   LoadingPulse: () => (/* binding */ LoadingPulse),\n/* harmony export */   LoadingSkeleton: () => (/* binding */ LoadingSkeleton),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingState: () => (/* binding */ LoadingState)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner,LoadingSkeleton,LoadingCard,LoadingOverlay,LoadingDots,LoadingPulse,LoadingProgress,LoadingState auto */ \n\n\nfunction LoadingSpinner(param) {\n    let { size = 'md', className } = param;\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-8 h-8',\n        lg: 'w-12 h-12'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('animate-spin rounded-full border-2 border-white/20 border-t-white', sizeClasses[size], className)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingSpinner;\nfunction LoadingSkeleton(param) {\n    let { className, lines = 1 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-2', className),\n        children: Array.from({\n            length: lines\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-4 bg-white/10 rounded animate-pulse\",\n                style: {\n                    width: \"\".concat(Math.random() * 40 + 60, \"%\"),\n                    animationDelay: \"\".concat(index * 0.1, \"s\")\n                }\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingSkeleton;\nfunction LoadingCard(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('glass-card p-6 space-y-4', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-white/10 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-4 bg-white/10 rounded animate-pulse w-1/3\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-white/10 rounded animate-pulse w-1/2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-white/10 rounded animate-pulse w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-white/10 rounded animate-pulse w-full\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-white/10 rounded animate-pulse w-2/3\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-2\",\n                children: Array.from({\n                    length: 3\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 w-16 bg-white/10 rounded animate-pulse\",\n                        style: {\n                            animationDelay: \"\".concat(index * 0.1, \"s\")\n                        }\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n_c2 = LoadingCard;\nfunction LoadingOverlay(param) {\n    let { isLoading, children, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative', className),\n        children: [\n            children,\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                            size: \"lg\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white text-sm\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_c3 = LoadingOverlay;\nfunction LoadingDots(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex space-x-1', className),\n        children: Array.from({\n            length: 3\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-2 h-2 bg-white rounded-full animate-bounce\",\n                style: {\n                    animationDelay: \"\".concat(index * 0.1, \"s\")\n                }\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_c4 = LoadingDots;\nfunction LoadingPulse(param) {\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('relative', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-blue-400 rounded-full animate-ping absolute\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-4 h-4 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_c5 = LoadingPulse;\nfunction LoadingProgress(param) {\n    let { progress, className, showPercentage = true } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('space-y-2', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-white/20 rounded-full h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: \"\".concat(Math.min(Math.max(progress, 0), 100), \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            showPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-sm text-white/60\",\n                children: [\n                    Math.round(progress),\n                    \"%\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n_c6 = LoadingProgress;\nfunction LoadingState(param) {\n    let { isLoading, error, children, loadingComponent, errorComponent, className } = param;\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center justify-center p-8', className),\n            children: errorComponent || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-400 text-lg\",\n                        children: \"⚠️ Error\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 183,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center justify-center p-8', className),\n            children: loadingComponent || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n                lineNumber: 196,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/ui/loading.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_c7 = LoadingState;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LoadingSpinner\");\n$RefreshReg$(_c1, \"LoadingSkeleton\");\n$RefreshReg$(_c2, \"LoadingCard\");\n$RefreshReg$(_c3, \"LoadingOverlay\");\n$RefreshReg$(_c4, \"LoadingDots\");\n$RefreshReg$(_c5, \"LoadingPulse\");\n$RefreshReg$(_c6, \"LoadingProgress\");\n$RefreshReg$(_c7, \"LoadingState\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/loading.tsx\n"));

/***/ })

});