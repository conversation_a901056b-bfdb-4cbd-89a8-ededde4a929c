"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMarketData */ \"(app-pages-browser)/./src/hooks/useMarketData.ts\");\n/* harmony import */ var _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSignals */ \"(app-pages-browser)/./src/hooks/useSignals.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./src/hooks/useWebSocket.ts\");\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./CryptoMetrics */ \"(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const { marketOverview, isLoading: marketLoading, refreshData } = (0,_hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData)();\n    const { filteredSignals: signals, isLoading: signalsLoading, refreshSignals, getSignalsByType } = (0,_hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals)();\n    const { isConnected, lastMessage, connectionStatus } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket)('mock://localhost');\n    const { showSignalNotification, requestPermission } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        try {\n            await Promise.all([\n                refreshData(),\n                refreshSignals()\n            ]);\n            setLastUpdate(new Date());\n        } catch (error) {\n            console.error('Failed to refresh data:', error);\n        } finally{\n            setIsRefreshing(false);\n        }\n    };\n    // Handle WebSocket messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            if ((lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.type) === 'signal_alert') {\n                showSignalNotification(lastMessage.data);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        lastMessage,\n        showSignalNotification\n    ]);\n    // Request notification permission on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            requestPermission();\n        }\n    }[\"Dashboard.useEffect\"], [\n        requestPermission\n    ]);\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        if (!marketOverview) return 50;\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketOverview.marketCapChange24h > 0) score += 10;\n        if (marketOverview.fearGreedIndex > 50) score += 10;\n        if (marketOverview.btcDominance > 45 && marketOverview.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = getSignalsByType('buy').filter((s)=>s.status === 'active');\n    const sellSignals = getSignalsByType('sell').filter((s)=>s.status === 'active');\n    const alertSignals = getSignalsByType('alert').filter((s)=>s.status === 'active');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-400' : 'bg-red-400')\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"WebSocket: \",\n                                                        connectionStatus\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                marketOverview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_5__.MarketOverview, {\n                    marketCap: marketOverview.totalMarketCap,\n                    marketCapChange: marketOverview.marketCapChange24h,\n                    volume24h: marketOverview.totalVolume24h,\n                    btcDominance: marketOverview.btcDominance,\n                    fearGreedIndex: marketOverview.fearGreedIndex,\n                    activeCoins: marketOverview.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_6__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_8__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_9__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_7__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"/cBKIm9TCyA7/qejlneiUefSI44=\", false, function() {\n    return [\n        _hooks_useMarketData__WEBPACK_IMPORTED_MODULE_2__.useMarketData,\n        _hooks_useSignals__WEBPACK_IMPORTED_MODULE_3__.useSignals,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useWebSocket,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});