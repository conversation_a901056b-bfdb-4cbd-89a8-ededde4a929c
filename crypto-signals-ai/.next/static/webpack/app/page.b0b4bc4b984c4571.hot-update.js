"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/Dashboard.tsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dashboard: () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarketOverview__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarketOverview */ \"(app-pages-browser)/./src/components/dashboard/MarketOverview.tsx\");\n/* harmony import */ var _signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../signals/SignalCard */ \"(app-pages-browser)/./src/components/signals/SignalCard.tsx\");\n/* harmony import */ var _indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../indicators/IndicatorMatrix */ \"(app-pages-browser)/./src/components/indicators/IndicatorMatrix.tsx\");\n/* harmony import */ var _charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../charts/MarketHeatmap */ \"(app-pages-browser)/./src/components/charts/MarketHeatmap.tsx\");\n/* harmony import */ var _CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CryptoMetrics */ \"(app-pages-browser)/./src/components/dashboard/CryptoMetrics.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Bell,RefreshCw,Settings,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* __next_internal_client_entry_do_not_use__ Dashboard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock data - In real app, this would come from APIs\nconst mockMarketData = {\n    totalMarketCap: 2340000000000,\n    totalVolume24h: 89000000000,\n    btcDominance: 52.4,\n    fearGreedIndex: 65,\n    activeCoins: 2847,\n    marketCapChange24h: 2.3,\n    lastUpdated: new Date()\n};\nconst mockSignals = [\n    {\n        id: '1',\n        symbol: 'BTC/USDT',\n        type: 'buy',\n        strength: 'strong',\n        priority: 'high',\n        score: 87,\n        confidence: 92,\n        timeframe: '4h',\n        timestamp: new Date(Date.now() - 2 * 60 * 1000),\n        entryPrice: 52350,\n        targetPrice: 54500,\n        stopLoss: 50800,\n        title: 'Strong Buy Signal - BTC/USDT',\n        description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',\n        reasoning: [\n            'RSI shows bullish signal with 85.2% strength',\n            'MACD shows bullish signal with 78.9% strength',\n            'Volume shows bullish signal with 72.1% strength'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 35,\n                weight: 0.15,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'MACD',\n                name: 'MACD',\n                type: 'momentum',\n                value: {\n                    macd: 0.5,\n                    signal: 0.3,\n                    histogram: 0.2\n                },\n                weight: 0.20,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 1250000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        riskReward: 2.2,\n        status: 'active',\n        tags: [\n            'buy',\n            'technical',\n            'momentum'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '2',\n        symbol: 'ETH/USDT',\n        type: 'sell',\n        strength: 'moderate',\n        priority: 'medium',\n        score: 72,\n        confidence: 78,\n        timeframe: '1h',\n        timestamp: new Date(Date.now() - 5 * 60 * 1000),\n        entryPrice: 3180,\n        targetPrice: 3050,\n        stopLoss: 3250,\n        title: 'Moderate Sell Signal - ETH/USDT',\n        description: 'Overbought conditions with high funding rates suggest potential correction.',\n        reasoning: [\n            'RSI shows bearish signal with 75.4% strength',\n            'Funding rate at 0.08% indicates overleveraged longs'\n        ],\n        indicators: [\n            {\n                id: 'RSI',\n                name: 'RSI',\n                type: 'momentum',\n                value: 78,\n                weight: 0.15,\n                status: 'bearish',\n                confirmation: true\n            },\n            {\n                id: 'FundingRate',\n                name: 'Funding Rate',\n                type: 'sentiment',\n                value: 0.08,\n                weight: 0.10,\n                status: 'bearish',\n                confirmation: true\n            }\n        ],\n        riskReward: 1.8,\n        status: 'active',\n        tags: [\n            'sell',\n            'technical',\n            'funding'\n        ],\n        category: 'technical_analysis'\n    },\n    {\n        id: '3',\n        symbol: 'SOL/USDT',\n        type: 'alert',\n        strength: 'strong',\n        priority: 'high',\n        score: 85,\n        confidence: 90,\n        timeframe: '15m',\n        timestamp: new Date(Date.now() - 1 * 60 * 1000),\n        title: 'Breakout Alert - SOL/USDT',\n        description: 'Price has broken above key resistance level with high volume confirmation.',\n        reasoning: [\n            'Price broke resistance at $98.50',\n            'Volume spike of 340% confirms breakout'\n        ],\n        indicators: [\n            {\n                id: 'SupportResistance',\n                name: 'Support/Resistance',\n                type: 'support_resistance',\n                value: 98.5,\n                weight: 0.12,\n                status: 'bullish',\n                confirmation: true\n            },\n            {\n                id: 'Volume',\n                name: 'Volume',\n                type: 'volume',\n                value: 2100000,\n                weight: 0.08,\n                status: 'bullish',\n                confirmation: true\n            }\n        ],\n        status: 'active',\n        tags: [\n            'breakout',\n            'alert',\n            'volume'\n        ],\n        category: 'price_action'\n    }\n];\nfunction Dashboard() {\n    _s();\n    const [signals, setSignals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockSignals);\n    const [marketData, setMarketData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockMarketData);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleRefresh = async ()=>{\n        setIsRefreshing(true);\n        // Simulate API call\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setLastUpdate(new Date());\n        setIsRefreshing(false);\n    };\n    const handleSignalClick = (signal)=>{\n        console.log('Signal clicked:', signal);\n    // In real app, this would open signal details modal\n    };\n    const getMarketHealthScore = ()=>{\n        // Simple calculation based on market data\n        let score = 50 // Base score\n        ;\n        if (marketData.marketCapChange24h > 0) score += 10;\n        if (marketData.fearGreedIndex > 50) score += 10;\n        if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10;\n        return Math.min(Math.max(score, 0), 100);\n    };\n    const marketHealthScore = getMarketHealthScore();\n    const getHealthStatus = (score)=>{\n        if (score >= 70) return {\n            label: 'BULLISH',\n            color: 'text-green-400',\n            bg: 'bg-green-400/20'\n        };\n        if (score >= 50) return {\n            label: 'NEUTRAL',\n            color: 'text-yellow-400',\n            bg: 'bg-yellow-400/20'\n        };\n        return {\n            label: 'BEARISH',\n            color: 'text-red-400',\n            bg: 'bg-red-400/20'\n        };\n    };\n    const healthStatus = getHealthStatus(marketHealthScore);\n    const activeSignals = signals.filter((s)=>s.status === 'active');\n    const buySignals = activeSignals.filter((s)=>s.type === 'buy');\n    const sellSignals = activeSignals.filter((s)=>s.type === 'sell');\n    const alertSignals = activeSignals.filter((s)=>s.type === 'alert');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Crypto Signals AI\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60\",\n                                    children: \"Advanced trading signals powered by 100+ technical indicators\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleRefresh,\n                                    disabled: isRefreshing,\n                                    className: \"flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 \".concat(isRefreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"glass-card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 rounded-lg \".concat(healthStatus.bg),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(healthStatus.color),\n                                                children: [\n                                                    \"Market Health: \",\n                                                    healthStatus.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: [\n                                                    marketHealthScore,\n                                                    \"/100\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        buySignals.length,\n                                                        \" Buy Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        sellSignals.length,\n                                                        \" Sell Signals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4 text-yellow-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        alertSignals.length,\n                                                        \" Alerts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Bell_RefreshCw_Settings_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Last update: \",\n                                                        lastUpdate.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarketOverview__WEBPACK_IMPORTED_MODULE_2__.MarketOverview, {\n                    marketCap: marketData.totalMarketCap,\n                    marketCapChange: marketData.marketCapChange24h,\n                    volume24h: marketData.totalVolume24h,\n                    btcDominance: marketData.btcDominance,\n                    fearGreedIndex: marketData.fearGreedIndex,\n                    activeCoins: marketData.activeCoins\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Active Signals\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"success\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                buySignals.length,\n                                                \" Buy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"danger\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                sellSignals.length,\n                                                \" Sell\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            variant: \"warning\",\n                                            className: \"text-sm\",\n                                            children: [\n                                                alertSignals.length,\n                                                \" Alerts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\",\n                            children: activeSignals.map((signal)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_signals_SignalCard__WEBPACK_IMPORTED_MODULE_3__.SignalCard, {\n                                    signal: signal,\n                                    onClick: handleSignalClick\n                                }, signal.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Market Heatmap\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_charts_MarketHeatmap__WEBPACK_IMPORTED_MODULE_5__.MarketHeatmap, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Crypto Metrics\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CryptoMetrics__WEBPACK_IMPORTED_MODULE_6__.CryptoMetrics, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-white mb-6\",\n                            children: \"Indicator Matrix\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_indicators_IndicatorMatrix__WEBPACK_IMPORTED_MODULE_4__.IndicatorMatrix, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/2025-crypto-sinais-ai/crypto-signals-ai/src/components/dashboard/Dashboard.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"BGoGPXQR2qfMMdRe7n0zoTgGTAI=\");\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Dashboard.tsx\n"));

/***/ })

});