/**
 * Application Configuration
 * Centralized configuration management for API keys and settings
 */

export interface ApiConfig {
  coinGecko: {
    apiKey: string | null
    baseUrl: string
    isConfigured: boolean
  }
  coinMarketCap: {
    apiKey: string | null
    baseUrl: string
    isConfigured: boolean
  }
  binance: {
    baseUrl: string
    isConfigured: boolean
  }
  alternativeMe: {
    baseUrl: string
    isConfigured: boolean
  }
}

export interface AppConfig {
  api: ApiConfig
  app: {
    environment: string
    baseUrl: string
    version: string
  }
  features: {
    realTimeData: boolean
    notifications: boolean
    webSocket: boolean
    caching: boolean
  }
  rateLimit: {
    enabled: boolean
    coinGecko: number
    coinMarketCap: number
  }
}

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string = ''): string => {
  if (typeof window !== 'undefined') {
    // Client-side: use Next.js public env vars
    return (window as any).__ENV__?.[key] || process.env[key] || fallback
  }
  // Server-side
  return process.env[key] || fallback
}

// API Configuration
const apiConfig: ApiConfig = {
  coinGecko: {
    apiKey: getEnvVar('NEXT_PUBLIC_COINGECKO_API_KEY'),
    baseUrl: 'https://api.coingecko.com/api/v3',
    isConfigured: !!getEnvVar('NEXT_PUBLIC_COINGECKO_API_KEY')
  },
  coinMarketCap: {
    apiKey: getEnvVar('NEXT_PUBLIC_COINMARKETCAP_API_KEY'),
    baseUrl: 'https://pro-api.coinmarketcap.com/v1',
    isConfigured: !!getEnvVar('NEXT_PUBLIC_COINMARKETCAP_API_KEY')
  },
  binance: {
    baseUrl: 'https://api.binance.com/api/v3',
    isConfigured: true // No API key required for public endpoints
  },
  alternativeMe: {
    baseUrl: 'https://api.alternative.me',
    isConfigured: true // No API key required
  }
}

// Main Application Configuration
export const config: AppConfig = {
  api: apiConfig,
  app: {
    environment: getEnvVar('NEXT_PUBLIC_APP_ENV', 'development'),
    baseUrl: getEnvVar('NEXT_PUBLIC_API_BASE_URL', 'http://localhost:3000'),
    version: '1.0.0'
  },
  features: {
    realTimeData: getEnvVar('NEXT_PUBLIC_WS_ENABLED', 'true') === 'true',
    notifications: true,
    webSocket: getEnvVar('NEXT_PUBLIC_WS_ENABLED', 'true') === 'true',
    caching: getEnvVar('NEXT_PUBLIC_CACHE_ENABLED', 'true') === 'true'
  },
  rateLimit: {
    enabled: getEnvVar('NEXT_PUBLIC_RATE_LIMIT_ENABLED', 'true') === 'true',
    coinGecko: 10, // calls per minute
    coinMarketCap: 30 // calls per minute
  }
}

// Configuration validation
export const validateConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check if at least one major API is configured
  if (!config.api.coinGecko.isConfigured && !config.api.coinMarketCap.isConfigured) {
    errors.push('No major API configured. Please set NEXT_PUBLIC_COINGECKO_API_KEY or NEXT_PUBLIC_COINMARKETCAP_API_KEY')
  }

  // Validate API keys format (basic validation)
  if (config.api.coinGecko.apiKey && !config.api.coinGecko.apiKey.startsWith('CG-')) {
    errors.push('CoinGecko API key should start with "CG-"')
  }

  if (config.api.coinMarketCap.apiKey && config.api.coinMarketCap.apiKey.length < 30) {
    errors.push('CoinMarketCap API key appears to be invalid (too short)')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Get configuration status
export const getConfigStatus = () => {
  const validation = validateConfig()
  
  return {
    ...validation,
    apis: {
      coinGecko: {
        configured: config.api.coinGecko.isConfigured,
        status: config.api.coinGecko.isConfigured ? 'ready' : 'not_configured'
      },
      coinMarketCap: {
        configured: config.api.coinMarketCap.isConfigured,
        status: config.api.coinMarketCap.isConfigured ? 'ready' : 'not_configured'
      },
      binance: {
        configured: config.api.binance.isConfigured,
        status: 'ready'
      },
      alternativeMe: {
        configured: config.api.alternativeMe.isConfigured,
        status: 'ready'
      }
    },
    hasAnyApi: config.api.coinGecko.isConfigured || config.api.coinMarketCap.isConfigured,
    recommendedSetup: !config.api.coinGecko.isConfigured && !config.api.coinMarketCap.isConfigured
  }
}

// Development helpers
export const isDevelopment = () => config.app.environment === 'development'
export const isProduction = () => config.app.environment === 'production'

// API helpers
export const hasApiKey = (provider: 'coinGecko' | 'coinMarketCap'): boolean => {
  return config.api[provider].isConfigured
}

export const getApiKey = (provider: 'coinGecko' | 'coinMarketCap'): string | null => {
  return config.api[provider].apiKey
}

export const getApiBaseUrl = (provider: keyof ApiConfig): string => {
  return config.api[provider].baseUrl
}

// Rate limiting helpers
export const getRateLimit = (provider: 'coinGecko' | 'coinMarketCap'): number => {
  return config.rateLimit[provider]
}

export const isRateLimitEnabled = (): boolean => {
  return config.rateLimit.enabled
}

// Feature flags
export const isFeatureEnabled = (feature: keyof AppConfig['features']): boolean => {
  return config.features[feature]
}

// Debug information (development only)
export const getDebugInfo = () => {
  if (!isDevelopment()) {
    return { message: 'Debug info only available in development' }
  }

  return {
    config: {
      ...config,
      api: {
        ...config.api,
        // Hide actual API keys in debug output
        coinGecko: {
          ...config.api.coinGecko,
          apiKey: config.api.coinGecko.apiKey ? '***CONFIGURED***' : null
        },
        coinMarketCap: {
          ...config.api.coinMarketCap,
          apiKey: config.api.coinMarketCap.apiKey ? '***CONFIGURED***' : null
        }
      }
    },
    validation: validateConfig(),
    status: getConfigStatus()
  }
}

// Export default config
export default config
