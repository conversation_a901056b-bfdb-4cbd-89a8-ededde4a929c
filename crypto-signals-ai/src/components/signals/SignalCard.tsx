'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Clock, 
  Target, 
  Shield,
  Activity
} from 'lucide-react'
import { Signal } from '@/types/signals'
import { formatCurrency, formatPercentage } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface SignalCardProps {
  signal: Signal
  onClick?: (signal: Signal) => void
}

export function SignalCard({ signal, onClick }: SignalCardProps) {
  const getSignalIcon = () => {
    switch (signal.type) {
      case 'buy':
        return <TrendingUp className="h-5 w-5 text-green-400" />
      case 'sell':
        return <TrendingDown className="h-5 w-5 text-red-400" />
      case 'alert':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />
      default:
        return <Activity className="h-5 w-5 text-blue-400" />
    }
  }

  const getSignalColor = () => {
    switch (signal.type) {
      case 'buy':
        return 'border-green-400/30 bg-green-400/5'
      case 'sell':
        return 'border-red-400/30 bg-red-400/5'
      case 'alert':
        return 'border-yellow-400/30 bg-yellow-400/5'
      default:
        return 'border-blue-400/30 bg-blue-400/5'
    }
  }

  const getStrengthColor = () => {
    switch (signal.strength) {
      case 'strong':
        return 'success'
      case 'moderate':
        return 'warning'
      case 'weak':
        return 'secondary'
      default:
        return 'default'
    }
  }

  const getPriorityColor = () => {
    switch (signal.priority) {
      case 'critical':
        return 'danger'
      case 'high':
        return 'warning'
      case 'medium':
        return 'default'
      case 'low':
        return 'secondary'
      default:
        return 'default'
    }
  }

  const calculatePotentialReturn = () => {
    if (!signal.entryPrice || !signal.targetPrice) return null
    
    const returnPercent = ((signal.targetPrice - signal.entryPrice) / signal.entryPrice) * 100
    return signal.type === 'sell' ? -returnPercent : returnPercent
  }

  const potentialReturn = calculatePotentialReturn()

  return (
    <Card 
      className={`glass-card cursor-pointer transition-all duration-300 hover:scale-[1.02] ${getSignalColor()}`}
      onClick={() => onClick?.(signal)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getSignalIcon()}
            <div>
              <CardTitle className="text-lg font-bold text-white">
                {signal.symbol}
              </CardTitle>
              <p className="text-sm text-white/60">
                {signal.timeframe} • {formatDistanceToNow(signal.timestamp, { addSuffix: true })}
              </p>
            </div>
          </div>
          <div className="flex flex-col items-end space-y-1">
            <Badge variant={getStrengthColor()}>
              {signal.strength.toUpperCase()}
            </Badge>
            <Badge variant={getPriorityColor()} className="text-xs">
              {signal.priority.toUpperCase()}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Signal Score and Confidence */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-white/60">Score:</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 h-2 bg-white/20 rounded-full">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    signal.score >= 80 ? 'bg-green-400' : 
                    signal.score >= 60 ? 'bg-yellow-400' : 'bg-red-400'
                  }`}
                  style={{ width: `${signal.score}%` }}
                />
              </div>
              <span className="text-sm font-medium text-white">
                {signal.score}/100
              </span>
            </div>
          </div>
          <div className="text-right">
            <span className="text-sm text-white/60">Confidence:</span>
            <span className="text-sm font-medium text-white ml-1">
              {signal.confidence}%
            </span>
          </div>
        </div>

        {/* Price Levels */}
        {signal.entryPrice && (
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <p className="text-white/60 mb-1">Entry</p>
              <p className="font-medium text-white">
                {formatCurrency(signal.entryPrice)}
              </p>
            </div>
            {signal.targetPrice && (
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Target className="h-3 w-3 text-white/60" />
                  <p className="text-white/60">Target</p>
                </div>
                <p className="font-medium text-green-400">
                  {formatCurrency(signal.targetPrice)}
                </p>
              </div>
            )}
            {signal.stopLoss && (
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Shield className="h-3 w-3 text-white/60" />
                  <p className="text-white/60">Stop</p>
                </div>
                <p className="font-medium text-red-400">
                  {formatCurrency(signal.stopLoss)}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Potential Return and Risk/Reward */}
        {potentialReturn && (
          <div className="flex justify-between items-center pt-2 border-t border-white/10">
            <div>
              <span className="text-sm text-white/60">Potential Return:</span>
              <span className={`text-sm font-medium ml-1 ${
                potentialReturn >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {formatPercentage(potentialReturn)}
              </span>
            </div>
            {signal.riskReward && (
              <div>
                <span className="text-sm text-white/60">R/R:</span>
                <span className="text-sm font-medium text-white ml-1">
                  1:{signal.riskReward.toFixed(2)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Signal Description */}
        <div className="pt-2">
          <p className="text-sm text-white/80 leading-relaxed">
            {signal.description}
          </p>
        </div>

        {/* Contributing Indicators */}
        <div className="flex flex-wrap gap-1 pt-2">
          {signal.indicators.slice(0, 4).map((indicator, index) => (
            <Badge 
              key={index}
              variant="outline" 
              className="text-xs border-white/20 text-white/70"
            >
              {indicator.name}
            </Badge>
          ))}
          {signal.indicators.length > 4 && (
            <Badge variant="outline" className="text-xs border-white/20 text-white/70">
              +{signal.indicators.length - 4} more
            </Badge>
          )}
        </div>

        {/* Expiration Timer */}
        {signal.expiresAt && (
          <div className="flex items-center space-x-2 pt-2 text-xs text-white/60">
            <Clock className="h-3 w-3" />
            <span>
              Expires {formatDistanceToNow(signal.expiresAt, { addSuffix: true })}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
