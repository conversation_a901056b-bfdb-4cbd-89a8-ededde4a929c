'use client'

import React, { useState, useEffect } from 'react'
import { marketDataService } from '@/services/api/marketDataService'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatPercentage, formatCurrency } from '@/lib/utils'

interface CryptoHeatmapData {
  symbol: string
  name: string
  price: number
  change24h: number
  marketCap: number
  volume24h: number
}

export function MarketHeatmap() {
  const [heatmapData, setHeatmapData] = useState<CryptoHeatmapData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real market data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const cryptoAssets = await marketDataService.getTopCryptoAssets(16) // Get top 16 for heatmap

        const transformedData: CryptoHeatmapData[] = cryptoAssets.map(asset => ({
          symbol: asset.symbol,
          name: asset.name,
          price: asset.price,
          change24h: asset.change24h,
          marketCap: asset.marketCap,
          volume24h: asset.volume24h
        }))

        setHeatmapData(transformedData)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch heatmap data')
        console.error('Heatmap data fetch error:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()

    // Refresh every 3 minutes
    const interval = setInterval(fetchData, 3 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])
  const getChangeColor = (change: number) => {
    if (change > 3) return 'bg-green-500'
    if (change > 1) return 'bg-green-400'
    if (change > 0) return 'bg-green-300'
    if (change > -1) return 'bg-red-300'
    if (change > -3) return 'bg-red-400'
    return 'bg-red-500'
  }

  const getTextColor = (change: number) => {
    return Math.abs(change) > 1 ? 'text-white' : 'text-gray-800'
  }

  const getSize = (marketCap: number) => {
    if (heatmapData.length === 0) return 'col-span-1 row-span-1'

    // Normalize market cap to determine size
    const maxCap = Math.max(...heatmapData.map(d => d.marketCap))
    const minCap = Math.min(...heatmapData.map(d => d.marketCap))
    const normalized = (marketCap - minCap) / (maxCap - minCap)

    // Return size classes based on normalized value
    if (normalized > 0.8) return 'col-span-4 row-span-3'
    if (normalized > 0.6) return 'col-span-3 row-span-2'
    if (normalized > 0.4) return 'col-span-2 row-span-2'
    if (normalized > 0.2) return 'col-span-2 row-span-1'
    return 'col-span-1 row-span-1'
  }

  if (isLoading) {
    return (
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white">
            Market Heatmap
          </CardTitle>
          <p className="text-sm text-white/60">
            Loading market data...
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-8 grid-rows-6 gap-2 h-96">
            {Array.from({ length: 16 }).map((_, index) => (
              <div
                key={index}
                className="bg-white/10 rounded-lg animate-pulse col-span-2 row-span-1"
              />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white">
            Market Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="text-red-400 mb-2">⚠️ Error Loading Heatmap</div>
            <p className="text-white/60 text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-card">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">
          Market Heatmap
        </CardTitle>
        <p className="text-sm text-white/60">
          Size represents market cap, color represents 24h change
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-8 grid-rows-6 gap-2 h-96">
          {heatmapData.map((crypto, index) => (
            <div
              key={crypto.symbol}
              className={`
                ${getSize(crypto.marketCap)}
                ${getChangeColor(crypto.change24h)}
                ${getTextColor(crypto.change24h)}
                rounded-lg p-3 flex flex-col justify-between
                transition-all duration-300 hover:scale-105 hover:z-10
                cursor-pointer relative group
              `}
            >
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 whitespace-nowrap">
                <div className="font-semibold">{crypto.name}</div>
                <div>Price: {formatCurrency(crypto.price)}</div>
                <div>Market Cap: {formatCurrency(crypto.marketCap)}</div>
                <div>Volume: {formatCurrency(crypto.volume24h)}</div>
                <div>24h Change: {formatPercentage(crypto.change24h)}</div>
              </div>

              {/* Content */}
              <div className="flex flex-col h-full justify-between">
                <div>
                  <div className="font-bold text-sm mb-1">
                    {crypto.symbol}
                  </div>
                  <div className="text-xs opacity-80">
                    {formatCurrency(crypto.price)}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold text-sm">
                    {formatPercentage(crypto.change24h)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="mt-6 flex items-center justify-between text-sm text-white/60">
          <div className="flex items-center space-x-4">
            <span>24h Change:</span>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span>&lt; -3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-300 rounded"></div>
              <span>-1% to -3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-300 rounded"></div>
              <span>0% to 1%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-400 rounded"></div>
              <span>1% to 3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>&gt; 3%</span>
            </div>
          </div>
          <div>
            <span>Size = Market Cap</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
