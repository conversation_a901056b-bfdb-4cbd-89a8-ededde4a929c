'use client'

import React from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatPercentage, formatCurrency } from '@/lib/utils'

interface CryptoHeatmapData {
  symbol: string
  name: string
  price: number
  change24h: number
  marketCap: number
  volume24h: number
}

const mockHeatmapData: CryptoHeatmapData[] = [
  { symbol: 'BTC', name: 'Bitcoin', price: 52350, change24h: 2.3, marketCap: 1020000000000, volume24h: 28000000000 },
  { symbol: 'ETH', name: 'Ethereum', price: 3180, change24h: 1.8, marketCap: 382000000000, volume24h: 15000000000 },
  { symbol: 'BNB', name: 'BNB', price: 315, change24h: -0.5, marketCap: 47000000000, volume24h: 1200000000 },
  { symbol: 'SOL', name: 'Sol<PERSON>', price: 98.5, change24h: 4.2, marketCap: 45000000000, volume24h: 2100000000 },
  { symbol: 'X<PERSON>', name: 'X<PERSON>', price: 0.52, change24h: -1.2, marketCap: 29000000000, volume24h: 1800000000 },
  { symbol: 'ADA', name: 'Cardano', price: 0.38, change24h: 0.8, marketCap: 13000000000, volume24h: 450000000 },
  { symbol: 'AVAX', name: 'Avalanche', price: 28.5, change24h: 3.1, marketCap: 11000000000, volume24h: 380000000 },
  { symbol: 'DOT', name: 'Polkadot', price: 6.2, change24h: -0.8, marketCap: 8500000000, volume24h: 220000000 },
  { symbol: 'MATIC', name: 'Polygon', price: 0.85, change24h: 2.1, marketCap: 8200000000, volume24h: 340000000 },
  { symbol: 'LINK', name: 'Chainlink', price: 14.2, change24h: 1.5, marketCap: 8800000000, volume24h: 280000000 },
  { symbol: 'UNI', name: 'Uniswap', price: 8.9, change24h: -2.1, marketCap: 6700000000, volume24h: 180000000 },
  { symbol: 'LTC', name: 'Litecoin', price: 92, change24h: 0.3, marketCap: 6900000000, volume24h: 420000000 },
  { symbol: 'ATOM', name: 'Cosmos', price: 7.8, change24h: 1.9, marketCap: 3100000000, volume24h: 95000000 },
  { symbol: 'FTM', name: 'Fantom', price: 0.42, change24h: 5.2, marketCap: 1200000000, volume24h: 85000000 },
  { symbol: 'ALGO', name: 'Algorand', price: 0.18, change24h: -1.5, marketCap: 1400000000, volume24h: 45000000 },
  { symbol: 'VET', name: 'VeChain', price: 0.025, change24h: 0.9, marketCap: 1800000000, volume24h: 32000000 }
]

export function MarketHeatmap() {
  const getChangeColor = (change: number) => {
    if (change > 3) return 'bg-green-500'
    if (change > 1) return 'bg-green-400'
    if (change > 0) return 'bg-green-300'
    if (change > -1) return 'bg-red-300'
    if (change > -3) return 'bg-red-400'
    return 'bg-red-500'
  }

  const getTextColor = (change: number) => {
    return Math.abs(change) > 1 ? 'text-white' : 'text-gray-800'
  }

  const getSize = (marketCap: number) => {
    // Normalize market cap to determine size
    const maxCap = Math.max(...mockHeatmapData.map(d => d.marketCap))
    const minCap = Math.min(...mockHeatmapData.map(d => d.marketCap))
    const normalized = (marketCap - minCap) / (maxCap - minCap)
    
    // Return size classes based on normalized value
    if (normalized > 0.8) return 'col-span-4 row-span-3'
    if (normalized > 0.6) return 'col-span-3 row-span-2'
    if (normalized > 0.4) return 'col-span-2 row-span-2'
    if (normalized > 0.2) return 'col-span-2 row-span-1'
    return 'col-span-1 row-span-1'
  }

  return (
    <Card className="glass-card">
      <CardHeader>
        <CardTitle className="text-xl font-bold text-white">
          Market Heatmap
        </CardTitle>
        <p className="text-sm text-white/60">
          Size represents market cap, color represents 24h change
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-8 grid-rows-6 gap-2 h-96">
          {mockHeatmapData.map((crypto, index) => (
            <div
              key={crypto.symbol}
              className={`
                ${getSize(crypto.marketCap)}
                ${getChangeColor(crypto.change24h)}
                ${getTextColor(crypto.change24h)}
                rounded-lg p-3 flex flex-col justify-between
                transition-all duration-300 hover:scale-105 hover:z-10
                cursor-pointer relative group
              `}
            >
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black/80 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-20 whitespace-nowrap">
                <div className="font-semibold">{crypto.name}</div>
                <div>Price: {formatCurrency(crypto.price)}</div>
                <div>Market Cap: {formatCurrency(crypto.marketCap)}</div>
                <div>Volume: {formatCurrency(crypto.volume24h)}</div>
                <div>24h Change: {formatPercentage(crypto.change24h)}</div>
              </div>

              {/* Content */}
              <div className="flex flex-col h-full justify-between">
                <div>
                  <div className="font-bold text-sm mb-1">
                    {crypto.symbol}
                  </div>
                  <div className="text-xs opacity-80">
                    {formatCurrency(crypto.price)}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold text-sm">
                    {formatPercentage(crypto.change24h)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="mt-6 flex items-center justify-between text-sm text-white/60">
          <div className="flex items-center space-x-4">
            <span>24h Change:</span>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span>&lt; -3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-300 rounded"></div>
              <span>-1% to -3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-300 rounded"></div>
              <span>0% to 1%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-400 rounded"></div>
              <span>1% to 3%</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span>&gt; 3%</span>
            </div>
          </div>
          <div>
            <span>Size = Market Cap</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
