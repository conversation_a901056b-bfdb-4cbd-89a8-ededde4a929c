'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, ExternalLink, Key, CheckCircle } from 'lucide-react'

interface ApiConfigurationGuideProps {
  onRetry?: () => void
}

export function ApiConfigurationGuide({ onRetry }: ApiConfigurationGuideProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <Card className="glass-card max-w-4xl w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-16 w-16 text-yellow-400" />
          </div>
          <CardTitle className="text-3xl font-bold text-white mb-2">
            API Configuration Required
          </CardTitle>
          <p className="text-white/70 text-lg">
            To use real market data, you need to configure API keys
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
            <h3 className="text-red-400 font-semibold mb-2 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Current Status
            </h3>
            <p className="text-white/80">
              No API keys detected. The application cannot fetch real market data without proper configuration.
            </p>
          </div>

          {/* Quick Setup */}
          <div className="grid md:grid-cols-2 gap-6">
            {/* CoinGecko */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Key className="h-5 w-5 mr-2 text-green-400" />
                  CoinGecko API (Recommended)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-white/70">
                  <div className="flex items-center mb-1">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-400" />
                    Free tier: 10-30 calls/min
                  </div>
                  <div className="flex items-center mb-1">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-400" />
                    No credit card required
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-green-400" />
                    Instant activation
                  </div>
                </div>
                
                <Button 
                  className="w-full bg-green-600 hover:bg-green-700"
                  onClick={() => window.open('https://www.coingecko.com/en/api/pricing', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Get Free API Key
                </Button>
              </CardContent>
            </Card>

            {/* CoinMarketCap */}
            <Card className="bg-white/5 border-white/10">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Key className="h-5 w-5 mr-2 text-blue-400" />
                  CoinMarketCap API (Alternative)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-white/70">
                  <div className="flex items-center mb-1">
                    <CheckCircle className="h-4 w-4 mr-2 text-blue-400" />
                    Free tier: 30 calls/min
                  </div>
                  <div className="flex items-center mb-1">
                    <CheckCircle className="h-4 w-4 mr-2 text-blue-400" />
                    10,000 calls/month
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-blue-400" />
                    Email verification required
                  </div>
                </div>
                
                <Button 
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  onClick={() => window.open('https://coinmarketcap.com/api/', '_blank')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Get Free API Key
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Setup Instructions */}
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-6">
            <h3 className="text-blue-400 font-semibold mb-4 text-lg">
              Setup Instructions
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  1
                </div>
                <div>
                  <p className="text-white font-medium">Create .env.local file</p>
                  <p className="text-white/70 text-sm">Copy .env.example to .env.local in your project root</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  2
                </div>
                <div>
                  <p className="text-white font-medium">Add your API key</p>
                  <div className="bg-black/30 rounded p-2 mt-1 font-mono text-sm text-green-400">
                    NEXT_PUBLIC_COINGECKO_API_KEY=your_key_here
                  </div>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  3
                </div>
                <div>
                  <p className="text-white font-medium">Restart the application</p>
                  <p className="text-white/70 text-sm">Run npm run dev again to load the new configuration</p>
                </div>
              </div>
            </div>
          </div>

          {/* Demo Mode */}
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4">
            <h3 className="text-yellow-400 font-semibold mb-2">
              Demo Mode Available
            </h3>
            <p className="text-white/80 text-sm mb-3">
              You can explore the application with simulated data while setting up your API keys.
            </p>
            {onRetry && (
              <Button 
                onClick={onRetry}
                className="bg-yellow-600 hover:bg-yellow-700"
              >
                Continue with Demo Data
              </Button>
            )}
          </div>

          {/* Help Links */}
          <div className="text-center space-y-2">
            <p className="text-white/60 text-sm">Need help?</p>
            <div className="flex justify-center space-x-4">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => window.open('https://github.com/your-repo/crypto-signals-ai/blob/main/API_SETUP.md', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Setup Guide
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => window.open('https://github.com/your-repo/crypto-signals-ai/issues', '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Get Support
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
