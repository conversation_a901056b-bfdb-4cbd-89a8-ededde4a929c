'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Settings, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  Target,
  Shield,
  BarChart3,
  Zap,
  Play,
  Pause,
  Edit,
  Copy,
  Trash2
} from 'lucide-react'

interface Strategy {
  id: string
  name: string
  description: string
  enabled: boolean
  performance: {
    winRate: number
    totalTrades: number
    profitFactor: number
    maxDrawdown: number
    avgReturn: number
  }
  config: {
    minScore: number
    maxRisk: number
    timeframes: string[]
    symbols: string[]
    indicators: string[]
  }
  lastUpdated: Date
}

const mockStrategies: Strategy[] = [
  {
    id: 'momentum_breakout',
    name: 'Momentum Breakout',
    description: 'Captures strong momentum moves with volume confirmation',
    enabled: true,
    performance: {
      winRate: 68.5,
      totalTrades: 247,
      profitFactor: 2.34,
      maxDrawdown: -12.8,
      avgReturn: 3.2
    },
    config: {
      minScore: 75,
      maxRisk: 2.0,
      timeframes: ['15m', '1h', '4h'],
      symbols: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT'],
      indicators: ['RSI', 'MACD', 'Volume', 'Bollinger Bands']
    },
    lastUpdated: new Date()
  },
  {
    id: 'mean_reversion',
    name: 'Mean Reversion',
    description: 'Trades oversold/overbought conditions with tight risk management',
    enabled: true,
    performance: {
      winRate: 72.1,
      totalTrades: 189,
      profitFactor: 1.89,
      maxDrawdown: -8.4,
      avgReturn: 1.8
    },
    config: {
      minScore: 70,
      maxRisk: 1.5,
      timeframes: ['5m', '15m', '1h'],
      symbols: ['BTC/USDT', 'ETH/USDT'],
      indicators: ['RSI', 'Stochastic', 'Bollinger %B', 'Support/Resistance']
    },
    lastUpdated: new Date()
  },
  {
    id: 'trend_following',
    name: 'Trend Following',
    description: 'Follows established trends with dynamic position sizing',
    enabled: false,
    performance: {
      winRate: 58.3,
      totalTrades: 156,
      profitFactor: 2.67,
      maxDrawdown: -18.2,
      avgReturn: 4.1
    },
    config: {
      minScore: 80,
      maxRisk: 3.0,
      timeframes: ['1h', '4h', '1d'],
      symbols: ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'ADA/USDT'],
      indicators: ['EMA', 'ADX', 'Supertrend', 'Higher Highs/Lower Lows']
    },
    lastUpdated: new Date()
  },
  {
    id: 'macro_alignment',
    name: 'Macro Alignment',
    description: 'Aligns crypto trades with macro market conditions',
    enabled: true,
    performance: {
      winRate: 64.7,
      totalTrades: 98,
      profitFactor: 2.12,
      maxDrawdown: -15.6,
      avgReturn: 2.9
    },
    config: {
      minScore: 85,
      maxRisk: 2.5,
      timeframes: ['4h', '1d'],
      symbols: ['BTC/USDT', 'ETH/USDT'],
      indicators: ['S&P 500', 'DXY', 'Bitcoin Dominance', 'Fed Policy']
    },
    lastUpdated: new Date()
  }
]

export function StrategySettings() {
  const [strategies, setStrategies] = useState<Strategy[]>(mockStrategies)
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null)

  const toggleStrategy = (strategyId: string) => {
    setStrategies(prev => prev.map(strategy => 
      strategy.id === strategyId 
        ? { ...strategy, enabled: !strategy.enabled }
        : strategy
    ))
  }

  const getPerformanceColor = (value: number, type: 'winRate' | 'profitFactor' | 'drawdown' | 'return') => {
    switch (type) {
      case 'winRate':
        return value >= 70 ? 'text-green-400' : value >= 60 ? 'text-yellow-400' : 'text-red-400'
      case 'profitFactor':
        return value >= 2 ? 'text-green-400' : value >= 1.5 ? 'text-yellow-400' : 'text-red-400'
      case 'drawdown':
        return value >= -10 ? 'text-green-400' : value >= -20 ? 'text-yellow-400' : 'text-red-400'
      case 'return':
        return value >= 3 ? 'text-green-400' : value >= 2 ? 'text-yellow-400' : 'text-red-400'
      default:
        return 'text-white'
    }
  }

  const enabledStrategies = strategies.filter(s => s.enabled)
  const totalTrades = strategies.reduce((sum, s) => sum + s.performance.totalTrades, 0)
  const avgWinRate = strategies.reduce((sum, s) => sum + s.performance.winRate, 0) / strategies.length

  return (
    <div className="space-y-6">
      {/* Strategy Overview */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Strategy Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{enabledStrategies.length}</div>
              <div className="text-sm text-white/60">Active Strategies</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{totalTrades}</div>
              <div className="text-sm text-white/60">Total Trades</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getPerformanceColor(avgWinRate, 'winRate')}`}>
                {avgWinRate.toFixed(1)}%
              </div>
              <div className="text-sm text-white/60">Avg Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {strategies.filter(s => s.enabled).length > 0 ? 'Active' : 'Inactive'}
              </div>
              <div className="text-sm text-white/60">System Status</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Strategy List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {strategies.map((strategy) => (
          <Card key={strategy.id} className="glass-card">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${strategy.enabled ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}`}>
                    {strategy.enabled ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                  </div>
                  <div>
                    <CardTitle className="text-lg font-bold text-white">{strategy.name}</CardTitle>
                    <p className="text-sm text-white/60">{strategy.description}</p>
                  </div>
                </div>
                <Badge variant={strategy.enabled ? 'success' : 'secondary'}>
                  {strategy.enabled ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Performance Metrics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/60">Win Rate:</span>
                    <span className={getPerformanceColor(strategy.performance.winRate, 'winRate')}>
                      {strategy.performance.winRate}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Profit Factor:</span>
                    <span className={getPerformanceColor(strategy.performance.profitFactor, 'profitFactor')}>
                      {strategy.performance.profitFactor.toFixed(2)}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/60">Max Drawdown:</span>
                    <span className={getPerformanceColor(strategy.performance.maxDrawdown, 'drawdown')}>
                      {strategy.performance.maxDrawdown}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">Avg Return:</span>
                    <span className={getPerformanceColor(strategy.performance.avgReturn, 'return')}>
                      {strategy.performance.avgReturn}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Configuration Summary */}
              <div className="space-y-2">
                <div className="text-sm text-white/60">Configuration:</div>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline" className="text-xs border-white/20 text-white/70">
                    Min Score: {strategy.config.minScore}
                  </Badge>
                  <Badge variant="outline" className="text-xs border-white/20 text-white/70">
                    Max Risk: {strategy.config.maxRisk}%
                  </Badge>
                  <Badge variant="outline" className="text-xs border-white/20 text-white/70">
                    {strategy.config.timeframes.length} Timeframes
                  </Badge>
                  <Badge variant="outline" className="text-xs border-white/20 text-white/70">
                    {strategy.config.symbols.length} Symbols
                  </Badge>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-2 border-t border-white/10">
                <div className="flex space-x-2">
                  <button
                    onClick={() => toggleStrategy(strategy.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      strategy.enabled 
                        ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30' 
                        : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                    }`}
                  >
                    {strategy.enabled ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </button>
                  <button className="p-2 bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 rounded-lg transition-colors">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="p-2 bg-yellow-500/20 text-yellow-400 hover:bg-yellow-500/30 rounded-lg transition-colors">
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
                <button className="p-2 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded-lg transition-colors">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Zap className="h-6 w-6 text-blue-400 mx-auto mb-2" />
              <div className="text-sm text-white">Create Strategy</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <BarChart3 className="h-6 w-6 text-green-400 mx-auto mb-2" />
              <div className="text-sm text-white">Backtest All</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Play className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
              <div className="text-sm text-white">Start All</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Pause className="h-6 w-6 text-red-400 mx-auto mb-2" />
              <div className="text-sm text-white">Stop All</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
