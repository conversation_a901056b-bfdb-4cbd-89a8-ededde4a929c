'use client'

import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone,
  Settings,
  Volume2,
  VolumeX,
  Check,
  X
} from 'lucide-react'

interface AlertChannel {
  id: string
  name: string
  type: 'email' | 'push' | 'webhook' | 'sms'
  enabled: boolean
  config: Record<string, any>
  icon: React.ReactNode
}

interface AlertRule {
  id: string
  name: string
  condition: string
  threshold: number
  enabled: boolean
  channels: string[]
  priority: 'low' | 'medium' | 'high' | 'critical'
}

export function AlertSettings() {
  const [channels, setChannels] = useState<AlertChannel[]>([
    {
      id: 'email',
      name: 'Email Notifications',
      type: 'email',
      enabled: true,
      config: { email: '<EMAIL>' },
      icon: <Mail className="h-4 w-4" />
    },
    {
      id: 'push',
      name: '<PERSON><PERSON><PERSON> Push',
      type: 'push',
      enabled: true,
      config: {},
      icon: <Bell className="h-4 w-4" />
    },
    {
      id: 'discord',
      name: 'Discord Webhook',
      type: 'webhook',
      enabled: false,
      config: { webhook_url: '' },
      icon: <MessageSquare className="h-4 w-4" />
    },
    {
      id: 'sms',
      name: 'SMS Alerts',
      type: 'sms',
      enabled: false,
      config: { phone: '' },
      icon: <Smartphone className="h-4 w-4" />
    }
  ])

  const [alertRules, setAlertRules] = useState<AlertRule[]>([
    {
      id: 'strong_buy',
      name: 'Strong Buy Signals',
      condition: 'Signal Score >= 80 AND Type = Buy',
      threshold: 80,
      enabled: true,
      channels: ['email', 'push'],
      priority: 'high'
    },
    {
      id: 'strong_sell',
      name: 'Strong Sell Signals',
      condition: 'Signal Score >= 80 AND Type = Sell',
      threshold: 80,
      enabled: true,
      channels: ['email', 'push'],
      priority: 'high'
    },
    {
      id: 'volatility_spike',
      name: 'High Volatility Alert',
      condition: 'ATR > 3% of Price',
      threshold: 3,
      enabled: true,
      channels: ['push'],
      priority: 'medium'
    },
    {
      id: 'funding_extreme',
      name: 'Extreme Funding Rates',
      condition: 'Funding Rate > 0.1% OR < -0.1%',
      threshold: 0.1,
      enabled: false,
      channels: ['email'],
      priority: 'medium'
    },
    {
      id: 'liquidation_cascade',
      name: 'Liquidation Cascade',
      condition: 'Liquidations > $100M in 1h',
      threshold: 100000000,
      enabled: true,
      channels: ['email', 'push', 'discord'],
      priority: 'critical'
    }
  ])

  const toggleChannel = (channelId: string) => {
    setChannels(prev => prev.map(channel => 
      channel.id === channelId 
        ? { ...channel, enabled: !channel.enabled }
        : channel
    ))
  }

  const toggleRule = (ruleId: string) => {
    setAlertRules(prev => prev.map(rule => 
      rule.id === ruleId 
        ? { ...rule, enabled: !rule.enabled }
        : rule
    ))
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'critical': return 'danger'
      case 'high': return 'warning'
      case 'medium': return 'default'
      case 'low': return 'secondary'
      default: return 'default'
    }
  }

  return (
    <div className="space-y-6">
      {/* Alert Channels */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Alert Channels</span>
          </CardTitle>
          <p className="text-sm text-white/60">
            Configure how you want to receive trading alerts
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {channels.map((channel) => (
            <div key={channel.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg ${channel.enabled ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}`}>
                  {channel.icon}
                </div>
                <div>
                  <div className="font-medium text-white">{channel.name}</div>
                  <div className="text-sm text-white/60">
                    {channel.type.charAt(0).toUpperCase() + channel.type.slice(1)} notifications
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Badge variant={channel.enabled ? 'success' : 'secondary'}>
                  {channel.enabled ? 'Enabled' : 'Disabled'}
                </Badge>
                <button
                  onClick={() => toggleChannel(channel.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    channel.enabled 
                      ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30' 
                      : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                  }`}
                >
                  {channel.enabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                </button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Alert Rules */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-white flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Alert Rules</span>
          </CardTitle>
          <p className="text-sm text-white/60">
            Define when and how alerts should be triggered
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {alertRules.map((rule) => (
            <div key={rule.id} className="p-4 bg-white/5 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${getPriorityColor(rule.priority)}`} />
                  <div>
                    <div className="font-medium text-white">{rule.name}</div>
                    <div className="text-sm text-white/60">{rule.condition}</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={getPriorityBadge(rule.priority) as any}>
                    {rule.priority.toUpperCase()}
                  </Badge>
                  <button
                    onClick={() => toggleRule(rule.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      rule.enabled 
                        ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30' 
                        : 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30'
                    }`}
                  >
                    {rule.enabled ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
                  </button>
                </div>
              </div>
              
              {/* Active Channels */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-white/60">Channels:</span>
                <div className="flex space-x-2">
                  {rule.channels.map((channelId) => {
                    const channel = channels.find(c => c.id === channelId)
                    return channel ? (
                      <div key={channelId} className="flex items-center space-x-1 px-2 py-1 bg-white/10 rounded text-xs text-white/80">
                        {channel.icon}
                        <span>{channel.name.split(' ')[0]}</span>
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Bell className="h-6 w-6 text-blue-400 mx-auto mb-2" />
              <div className="text-sm text-white">Test Alerts</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Settings className="h-6 w-6 text-green-400 mx-auto mb-2" />
              <div className="text-sm text-white">Export Config</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <Volume2 className="h-6 w-6 text-yellow-400 mx-auto mb-2" />
              <div className="text-sm text-white">Enable All</div>
            </button>
            
            <button className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-colors text-center">
              <VolumeX className="h-6 w-6 text-red-400 mx-auto mb-2" />
              <div className="text-sm text-white">Disable All</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
