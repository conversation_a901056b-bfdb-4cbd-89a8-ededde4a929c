'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, TrendingDown, Activity, DollarSign } from 'lucide-react'
import { formatCurrency, formatPercentage, formatNumber } from '@/lib/utils'

interface MarketOverviewProps {
  marketCap: number
  marketCapChange: number
  volume24h: number
  btcDominance: number
  fearGreedIndex: number
  activeCoins: number
}

export function MarketOverview({
  marketCap,
  marketCapChange,
  volume24h,
  btcDominance,
  fearGreedIndex,
  activeCoins
}: MarketOverviewProps) {
  const getMarketSentiment = (score: number) => {
    if (score >= 75) return { label: 'Extreme Greed', color: 'bg-green-500', icon: TrendingUp }
    if (score >= 55) return { label: 'Greed', color: 'bg-green-400', icon: TrendingUp }
    if (score >= 45) return { label: 'Neutral', color: 'bg-yellow-500', icon: Activity }
    if (score >= 25) return { label: 'Fear', color: 'bg-red-400', icon: TrendingDown }
    return { label: 'Extreme Fear', color: 'bg-red-500', icon: TrendingDown }
  }

  const sentiment = getMarketSentiment(fearGreedIndex)
  const SentimentIcon = sentiment.icon

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Market Cap */}
      <Card className="glass-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-white/80">
            Total Market Cap
          </CardTitle>
          <DollarSign className="h-4 w-4 text-white/60" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">
            {formatCurrency(marketCap, 'USD')}
          </div>
          <div className="flex items-center space-x-2 mt-2">
            {marketCapChange >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-400" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-400" />
            )}
            <span className={`text-sm ${marketCapChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercentage(marketCapChange)}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* 24h Volume */}
      <Card className="glass-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-white/80">
            24h Volume
          </CardTitle>
          <Activity className="h-4 w-4 text-white/60" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">
            {formatCurrency(volume24h, 'USD')}
          </div>
          <p className="text-xs text-white/60 mt-2">
            {formatNumber(activeCoins)} active coins
          </p>
        </CardContent>
      </Card>

      {/* BTC Dominance */}
      <Card className="glass-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-white/80">
            BTC Dominance
          </CardTitle>
          <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center">
            <span className="text-xs font-bold text-white">₿</span>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white">
            {formatPercentage(btcDominance, 1)}
          </div>
          <div className="w-full bg-white/20 rounded-full h-2 mt-2">
            <div 
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${btcDominance}%` }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Fear & Greed Index */}
      <Card className="glass-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-white/80">
            Fear & Greed Index
          </CardTitle>
          <SentimentIcon className="h-4 w-4 text-white/60" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-3">
            <div className="text-2xl font-bold text-white">
              {fearGreedIndex}
            </div>
            <Badge 
              className={`${sentiment.color} text-white border-none`}
            >
              {sentiment.label}
            </Badge>
          </div>
          <div className="w-full bg-white/20 rounded-full h-2 mt-2">
            <div 
              className={`${sentiment.color} h-2 rounded-full transition-all duration-300`}
              style={{ width: `${fearGreedIndex}%` }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Glass card styles to be added to globals.css
export const glassCardStyles = `
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
`
