'use client'

import React, { useState, useEffect } from 'react'
import { MarketOverview } from './MarketOverview'
import { SignalCard } from '../signals/SignalCard'
import { IndicatorMatrix } from '../indicators/IndicatorMatrix'
import { MarketHeatmap } from '../charts/MarketHeatmap'
import { CryptoMetrics } from './CryptoMetrics'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  RefreshCw,
  Settings,
  Bell
} from 'lucide-react'
import { Signal } from '@/types/signals'
import { MarketOverview as MarketOverviewType } from '@/types/market'

// Mock data - In real app, this would come from APIs
const mockMarketData: MarketOverviewType = {
  totalMarketCap: 2340000000000,
  totalVolume24h: 89000000000,
  btcDominance: 52.4,
  fearGreedIndex: 65,
  activeCoins: 2847,
  marketCapChange24h: 2.3,
  lastUpdated: new Date()
}

const mockSignals: Signal[] = [
  {
    id: '1',
    symbol: 'BTC/USDT',
    type: 'buy',
    strength: 'strong',
    priority: 'high',
    score: 87,
    confidence: 92,
    timeframe: '4h',
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    entryPrice: 52350,
    targetPrice: 54500,
    stopLoss: 50800,
    title: 'Strong Buy Signal - BTC/USDT',
    description: 'Multiple indicators confirm bullish momentum with RSI oversold recovery and MACD bullish crossover.',
    reasoning: [
      'RSI shows bullish signal with 85.2% strength',
      'MACD shows bullish signal with 78.9% strength',
      'Volume shows bullish signal with 72.1% strength'
    ],
    indicators: [
      { id: 'RSI', name: 'RSI', type: 'momentum', value: 35, weight: 0.15, status: 'bullish', confirmation: true },
      { id: 'MACD', name: 'MACD', type: 'momentum', value: { macd: 0.5, signal: 0.3, histogram: 0.2 }, weight: 0.20, status: 'bullish', confirmation: true },
      { id: 'Volume', name: 'Volume', type: 'volume', value: 1250000, weight: 0.08, status: 'bullish', confirmation: true }
    ],
    riskReward: 2.2,
    status: 'active',
    tags: ['buy', 'technical', 'momentum'],
    category: 'technical_analysis'
  },
  {
    id: '2',
    symbol: 'ETH/USDT',
    type: 'sell',
    strength: 'moderate',
    priority: 'medium',
    score: 72,
    confidence: 78,
    timeframe: '1h',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    entryPrice: 3180,
    targetPrice: 3050,
    stopLoss: 3250,
    title: 'Moderate Sell Signal - ETH/USDT',
    description: 'Overbought conditions with high funding rates suggest potential correction.',
    reasoning: [
      'RSI shows bearish signal with 75.4% strength',
      'Funding rate at 0.08% indicates overleveraged longs'
    ],
    indicators: [
      { id: 'RSI', name: 'RSI', type: 'momentum', value: 78, weight: 0.15, status: 'bearish', confirmation: true },
      { id: 'FundingRate', name: 'Funding Rate', type: 'sentiment', value: 0.08, weight: 0.10, status: 'bearish', confirmation: true }
    ],
    riskReward: 1.8,
    status: 'active',
    tags: ['sell', 'technical', 'funding'],
    category: 'technical_analysis'
  },
  {
    id: '3',
    symbol: 'SOL/USDT',
    type: 'alert',
    strength: 'strong',
    priority: 'high',
    score: 85,
    confidence: 90,
    timeframe: '15m',
    timestamp: new Date(Date.now() - 1 * 60 * 1000),
    title: 'Breakout Alert - SOL/USDT',
    description: 'Price has broken above key resistance level with high volume confirmation.',
    reasoning: [
      'Price broke resistance at $98.50',
      'Volume spike of 340% confirms breakout'
    ],
    indicators: [
      { id: 'SupportResistance', name: 'Support/Resistance', type: 'support_resistance', value: 98.5, weight: 0.12, status: 'bullish', confirmation: true },
      { id: 'Volume', name: 'Volume', type: 'volume', value: 2100000, weight: 0.08, status: 'bullish', confirmation: true }
    ],
    status: 'active',
    tags: ['breakout', 'alert', 'volume'],
    category: 'price_action'
  }
]

export function Dashboard() {
  const [signals, setSignals] = useState<Signal[]>(mockSignals)
  const [marketData, setMarketData] = useState<MarketOverviewType>(mockMarketData)
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    setLastUpdate(new Date())
    setIsRefreshing(false)
  }

  const handleSignalClick = (signal: Signal) => {
    console.log('Signal clicked:', signal)
    // In real app, this would open signal details modal
  }

  const getMarketHealthScore = () => {
    // Simple calculation based on market data
    let score = 50 // Base score
    
    if (marketData.marketCapChange24h > 0) score += 10
    if (marketData.fearGreedIndex > 50) score += 10
    if (marketData.btcDominance > 45 && marketData.btcDominance < 60) score += 10
    
    return Math.min(Math.max(score, 0), 100)
  }

  const marketHealthScore = getMarketHealthScore()
  const getHealthStatus = (score: number) => {
    if (score >= 70) return { label: 'BULLISH', color: 'text-green-400', bg: 'bg-green-400/20' }
    if (score >= 50) return { label: 'NEUTRAL', color: 'text-yellow-400', bg: 'bg-yellow-400/20' }
    return { label: 'BEARISH', color: 'text-red-400', bg: 'bg-red-400/20' }
  }

  const healthStatus = getHealthStatus(marketHealthScore)

  const activeSignals = signals.filter(s => s.status === 'active')
  const buySignals = activeSignals.filter(s => s.type === 'buy')
  const sellSignals = activeSignals.filter(s => s.type === 'sell')
  const alertSignals = activeSignals.filter(s => s.type === 'alert')

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Crypto Signals AI
            </h1>
            <p className="text-white/60">
              Advanced trading signals powered by 100+ technical indicators
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            <button className="p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200">
              <Bell className="h-4 w-4" />
            </button>
            <button className="p-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 text-white transition-all duration-200">
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Market Health Indicator */}
        <Card className="glass-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`px-4 py-2 rounded-lg ${healthStatus.bg}`}>
                  <span className={`font-bold text-lg ${healthStatus.color}`}>
                    Market Health: {healthStatus.label}
                  </span>
                </div>
                <div className="text-white">
                  <span className="text-2xl font-bold">{marketHealthScore}/100</span>
                </div>
              </div>
              <div className="flex items-center space-x-6 text-sm text-white/60">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-green-400" />
                  <span>{buySignals.length} Buy Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingDown className="h-4 w-4 text-red-400" />
                  <span>{sellSignals.length} Sell Signals</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span>{alertSignals.length} Alerts</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-blue-400" />
                  <span>Last update: {lastUpdate.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Market Overview */}
        <MarketOverview
          marketCap={marketData.totalMarketCap}
          marketCapChange={marketData.marketCapChange24h}
          volume24h={marketData.totalVolume24h}
          btcDominance={marketData.btcDominance}
          fearGreedIndex={marketData.fearGreedIndex}
          activeCoins={marketData.activeCoins}
        />

        {/* Active Signals */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Active Signals</h2>
            <div className="flex space-x-2">
              <Badge variant="success" className="text-sm">
                {buySignals.length} Buy
              </Badge>
              <Badge variant="danger" className="text-sm">
                {sellSignals.length} Sell
              </Badge>
              <Badge variant="warning" className="text-sm">
                {alertSignals.length} Alerts
              </Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {activeSignals.map((signal) => (
              <SignalCard
                key={signal.id}
                signal={signal}
                onClick={handleSignalClick}
              />
            ))}
          </div>
        </div>

        {/* Market Heatmap */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Market Heatmap</h2>
          <MarketHeatmap />
        </div>

        {/* Crypto-Specific Metrics */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Crypto Metrics</h2>
          <CryptoMetrics />
        </div>

        {/* Indicator Matrix */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-6">Indicator Matrix</h2>
          <IndicatorMatrix />
        </div>
      </div>
    </div>
  )
}
