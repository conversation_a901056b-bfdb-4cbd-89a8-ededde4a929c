'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Zap,
  DollarSign,
  Users,
  BarChart3,
  AlertTriangle
} from 'lucide-react'
import { formatPercentage, formatCurrency, formatNumber } from '@/lib/utils'

interface FundingRateData {
  exchange: string
  symbol: string
  rate: number
  nextFunding: string
}

interface LiquidationData {
  timeframe: string
  longs: number
  shorts: number
  total: number
}

interface OnChainData {
  metric: string
  value: string
  change24h: number
  status: 'bullish' | 'bearish' | 'neutral'
}

const mockFundingRates: FundingRateData[] = [
  { exchange: 'Binance', symbol: 'BTCUSDT', rate: 0.0125, nextFunding: '4h 23m' },
  { exchange: 'Bybit', symbol: 'BTCUSDT', rate: 0.0089, nextFunding: '4h 23m' },
  { exchange: 'OKX', symbol: 'BTCUSDT', rate: 0.0156, nextFunding: '4h 23m' },
  { exchange: 'Binance', symbol: 'ETHUSDT', rate: 0.0078, nextFunding: '4h 23m' },
  { exchange: 'Bybit', symbol: 'ETHUSDT', rate: 0.0092, nextFunding: '4h 23m' },
  { exchange: 'OKX', symbol: 'ETHUSDT', rate: 0.0134, nextFunding: '4h 23m' }
]

const mockLiquidations: LiquidationData[] = [
  { timeframe: '1h', longs: 12500000, shorts: 8200000, total: 20700000 },
  { timeframe: '4h', longs: 45000000, shorts: 28000000, total: 73000000 },
  { timeframe: '24h', longs: 180000000, shorts: 95000000, total: 275000000 }
]

const mockOnChainData: OnChainData[] = [
  { metric: 'Exchange Inflow', value: '-2,450 BTC', change24h: -15.2, status: 'bullish' },
  { metric: 'Exchange Outflow', value: '+3,890 BTC', change24h: 22.8, status: 'bullish' },
  { metric: 'Whale Transactions', value: '1,247', change24h: 8.5, status: 'neutral' },
  { metric: 'Active Addresses', value: '985,432', change24h: 3.2, status: 'bullish' },
  { metric: 'Network Hash Rate', value: '450.2 EH/s', change24h: 1.8, status: 'bullish' },
  { metric: 'Mining Difficulty', value: '62.46 T', change24h: 0.5, status: 'neutral' }
]

export function CryptoMetrics() {
  const getFundingRateColor = (rate: number) => {
    if (rate > 0.02) return 'text-red-400'
    if (rate > 0.01) return 'text-yellow-400'
    if (rate > 0) return 'text-green-400'
    if (rate > -0.01) return 'text-blue-400'
    return 'text-purple-400'
  }

  const getFundingRateStatus = (rate: number) => {
    if (rate > 0.02) return 'Extreme Greed'
    if (rate > 0.01) return 'Greed'
    if (rate > 0) return 'Bullish'
    if (rate > -0.01) return 'Bearish'
    return 'Extreme Fear'
  }

  const getStatusIcon = (status: 'bullish' | 'bearish' | 'neutral') => {
    switch (status) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-yellow-400" />
    }
  }

  const averageFundingBTC = mockFundingRates
    .filter(f => f.symbol === 'BTCUSDT')
    .reduce((sum, f) => sum + f.rate, 0) / 3

  const averageFundingETH = mockFundingRates
    .filter(f => f.symbol === 'ETHUSDT')
    .reduce((sum, f) => sum + f.rate, 0) / 3

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Funding Rates */}
      <Card className="glass-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Funding Rates</span>
            </CardTitle>
            <Badge variant="outline" className="border-white/20 text-white/70">
              Live
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* BTC Average */}
          <div className="p-3 bg-white/5 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-white">BTC/USDT Average</span>
              <span className={`font-bold ${getFundingRateColor(averageFundingBTC)}`}>
                {formatPercentage(averageFundingBTC * 100, 4)}
              </span>
            </div>
            <div className="text-sm text-white/60">
              Status: {getFundingRateStatus(averageFundingBTC)}
            </div>
          </div>

          {/* ETH Average */}
          <div className="p-3 bg-white/5 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-white">ETH/USDT Average</span>
              <span className={`font-bold ${getFundingRateColor(averageFundingETH)}`}>
                {formatPercentage(averageFundingETH * 100, 4)}
              </span>
            </div>
            <div className="text-sm text-white/60">
              Status: {getFundingRateStatus(averageFundingETH)}
            </div>
          </div>

          {/* Individual Rates */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white/80">By Exchange</h4>
            {mockFundingRates.map((rate, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <span className="text-white/60">{rate.exchange}</span>
                  <span className="text-white/80">{rate.symbol}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={getFundingRateColor(rate.rate)}>
                    {formatPercentage(rate.rate * 100, 4)}
                  </span>
                  <span className="text-white/40 text-xs">{rate.nextFunding}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Liquidations */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Liquidations</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {mockLiquidations.map((liq, index) => (
            <div key={index} className="p-3 bg-white/5 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <span className="font-semibold text-white">{liq.timeframe}</span>
                <span className="text-lg font-bold text-white">
                  {formatCurrency(liq.total)}
                </span>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-red-400 font-medium">
                    {formatCurrency(liq.longs)}
                  </div>
                  <div className="text-white/60">Longs</div>
                </div>
                <div className="text-center">
                  <div className="text-green-400 font-medium">
                    {formatCurrency(liq.shorts)}
                  </div>
                  <div className="text-white/60">Shorts</div>
                </div>
              </div>

              {/* Ratio Bar */}
              <div className="mt-3">
                <div className="flex h-2 rounded-full overflow-hidden">
                  <div 
                    className="bg-red-400"
                    style={{ width: `${(liq.longs / liq.total) * 100}%` }}
                  />
                  <div 
                    className="bg-green-400"
                    style={{ width: `${(liq.shorts / liq.total) * 100}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-white/60 mt-1">
                  <span>{((liq.longs / liq.total) * 100).toFixed(1)}% Longs</span>
                  <span>{((liq.shorts / liq.total) * 100).toFixed(1)}% Shorts</span>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* On-Chain Metrics */}
      <Card className="glass-card lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>On-Chain Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {mockOnChainData.map((metric, index) => (
              <div key={index} className="p-4 bg-white/5 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-white/60">{metric.metric}</span>
                  {getStatusIcon(metric.status)}
                </div>
                
                <div className="text-lg font-bold text-white mb-1">
                  {metric.value}
                </div>
                
                <div className="flex items-center space-x-2">
                  {metric.change24h >= 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-400" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-400" />
                  )}
                  <span className={`text-sm ${
                    metric.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatPercentage(metric.change24h)} 24h
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
