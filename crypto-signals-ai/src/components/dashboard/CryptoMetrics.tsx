'use client'

import React, { useState, useEffect } from 'react'
import { cryptoMetricsService } from '@/services/api/cryptoMetricsService'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Zap,
  DollarSign,
  Users,
  BarChart3,
  AlertTriangle
} from 'lucide-react'
import { formatPercentage, formatCurrency, formatNumber } from '@/lib/utils'

interface FundingRateData {
  exchange: string
  symbol: string
  rate: number
  nextFunding: string
}

interface LiquidationData {
  timeframe: string
  longs: number
  shorts: number
  total: number
}

interface OnChainData {
  metric: string
  value: string
  change24h: number
  status: 'bullish' | 'bearish' | 'neutral'
}

// Real-time data interfaces
interface FundingRateData {
  exchange: string
  symbol: string
  rate: number
  nextFunding: string
}

interface LiquidationData {
  timeframe: string
  longs: number
  shorts: number
  total: number
}

interface OnChainData {
  metric: string
  value: string
  change24h: number
  status: 'bullish' | 'bearish' | 'neutral'
}

export function CryptoMetrics() {
  const [fundingRates, setFundingRates] = useState<FundingRateData[]>([])
  const [liquidations, setLiquidations] = useState<LiquidationData[]>([])
  const [onChainData, setOnChainData] = useState<OnChainData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const metrics = await cryptoMetricsService.getAllMetrics()

        // Transform funding rates data
        const transformedFundingRates = metrics.fundingRates.map((rate: any) => ({
          exchange: rate.exchange,
          symbol: rate.symbol,
          rate: rate.rate,
          nextFunding: formatTimeUntil(rate.nextFundingTime)
        }))

        setFundingRates(transformedFundingRates)
        setLiquidations(metrics.liquidationData)
        setOnChainData(metrics.onChainMetrics)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch crypto metrics')
        console.error('Crypto metrics fetch error:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()

    // Refresh every 5 minutes
    const interval = setInterval(fetchData, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  // Helper function to format time until next funding
  const formatTimeUntil = (date: Date): string => {
    const now = new Date()
    const diff = date.getTime() - now.getTime()
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    return `${hours}h ${minutes}m`
  }
  const getFundingRateColor = (rate: number) => {
    if (rate > 0.02) return 'text-red-400'
    if (rate > 0.01) return 'text-yellow-400'
    if (rate > 0) return 'text-green-400'
    if (rate > -0.01) return 'text-blue-400'
    return 'text-purple-400'
  }

  const getFundingRateStatus = (rate: number) => {
    if (rate > 0.02) return 'Extreme Greed'
    if (rate > 0.01) return 'Greed'
    if (rate > 0) return 'Bullish'
    if (rate > -0.01) return 'Bearish'
    return 'Extreme Fear'
  }

  const getStatusIcon = (status: 'bullish' | 'bearish' | 'neutral') => {
    switch (status) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-yellow-400" />
    }
  }

  const averageFundingBTC = fundingRates
    .filter(f => f.symbol === 'BTCUSDT')
    .reduce((sum, f) => sum + f.rate, 0) / Math.max(1, fundingRates.filter(f => f.symbol === 'BTCUSDT').length)

  const averageFundingETH = fundingRates
    .filter(f => f.symbol === 'ETHUSDT')
    .reduce((sum, f) => sum + f.rate, 0) / Math.max(1, fundingRates.filter(f => f.symbol === 'ETHUSDT').length)

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="glass-card animate-pulse">
            <CardHeader>
              <div className="h-6 bg-white/10 rounded w-1/3"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 bg-white/10 rounded w-full"></div>
                <div className="h-4 bg-white/10 rounded w-2/3"></div>
                <div className="h-4 bg-white/10 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className="glass-card">
        <CardContent className="p-6 text-center">
          <div className="text-red-400 mb-2">⚠️ Error Loading Crypto Metrics</div>
          <p className="text-white/60 text-sm">{error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Funding Rates */}
      <Card className="glass-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Funding Rates</span>
            </CardTitle>
            <Badge variant="outline" className="border-white/20 text-white/70">
              Live
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* BTC Average */}
          <div className="p-3 bg-white/5 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-white">BTC/USDT Average</span>
              <span className={`font-bold ${getFundingRateColor(averageFundingBTC)}`}>
                {formatPercentage(averageFundingBTC * 100, 4)}
              </span>
            </div>
            <div className="text-sm text-white/60">
              Status: {getFundingRateStatus(averageFundingBTC)}
            </div>
          </div>

          {/* ETH Average */}
          <div className="p-3 bg-white/5 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-white">ETH/USDT Average</span>
              <span className={`font-bold ${getFundingRateColor(averageFundingETH)}`}>
                {formatPercentage(averageFundingETH * 100, 4)}
              </span>
            </div>
            <div className="text-sm text-white/60">
              Status: {getFundingRateStatus(averageFundingETH)}
            </div>
          </div>

          {/* Individual Rates */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-white/80">By Exchange</h4>
            {fundingRates.map((rate, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <span className="text-white/60">{rate.exchange}</span>
                  <span className="text-white/80">{rate.symbol}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={getFundingRateColor(rate.rate)}>
                    {formatPercentage(rate.rate * 100, 4)}
                  </span>
                  <span className="text-white/40 text-xs">{rate.nextFunding}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Liquidations */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Liquidations</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {liquidations.map((liq, index) => (
            <div key={index} className="p-3 bg-white/5 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <span className="font-semibold text-white">{liq.timeframe}</span>
                <span className="text-lg font-bold text-white">
                  {formatCurrency(liq.total)}
                </span>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-red-400 font-medium">
                    {formatCurrency(liq.longs)}
                  </div>
                  <div className="text-white/60">Longs</div>
                </div>
                <div className="text-center">
                  <div className="text-green-400 font-medium">
                    {formatCurrency(liq.shorts)}
                  </div>
                  <div className="text-white/60">Shorts</div>
                </div>
              </div>

              {/* Ratio Bar */}
              <div className="mt-3">
                <div className="flex h-2 rounded-full overflow-hidden">
                  <div 
                    className="bg-red-400"
                    style={{ width: `${(liq.longs / liq.total) * 100}%` }}
                  />
                  <div 
                    className="bg-green-400"
                    style={{ width: `${(liq.shorts / liq.total) * 100}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-white/60 mt-1">
                  <span>{((liq.longs / liq.total) * 100).toFixed(1)}% Longs</span>
                  <span>{((liq.shorts / liq.total) * 100).toFixed(1)}% Shorts</span>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* On-Chain Metrics */}
      <Card className="glass-card lg:col-span-2">
        <CardHeader>
          <CardTitle className="text-lg font-bold text-white flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>On-Chain Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {onChainData.map((metric, index) => (
              <div key={index} className="p-4 bg-white/5 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-white/60">{metric.metric}</span>
                  {getStatusIcon(metric.status)}
                </div>
                
                <div className="text-lg font-bold text-white mb-1">
                  {metric.value}
                </div>
                
                <div className="flex items-center space-x-2">
                  {metric.change24h >= 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-400" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-400" />
                  )}
                  <span className={`text-sm ${
                    metric.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatPercentage(metric.change24h)} 24h
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
