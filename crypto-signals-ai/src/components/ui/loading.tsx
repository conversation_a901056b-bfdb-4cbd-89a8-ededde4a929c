'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-white/20 border-t-white',
        sizeClasses[size],
        className
      )}
    />
  )
}

interface LoadingSkeletonProps {
  className?: string
  lines?: number
}

export function LoadingSkeleton({ className, lines = 1 }: LoadingSkeletonProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className="h-4 bg-white/10 rounded animate-pulse"
          style={{
            width: `${Math.random() * 40 + 60}%`,
            animationDelay: `${index * 0.1}s`
          }}
        />
      ))}
    </div>
  )
}

interface LoadingCardProps {
  className?: string
}

export function LoadingCard({ className }: LoadingCardProps) {
  return (
    <div className={cn('glass-card p-6 space-y-4', className)}>
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-white/10 rounded-full animate-pulse" />
        <div className="space-y-2 flex-1">
          <div className="h-4 bg-white/10 rounded animate-pulse w-1/3" />
          <div className="h-3 bg-white/10 rounded animate-pulse w-1/2" />
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="h-6 bg-white/10 rounded animate-pulse w-3/4" />
        <div className="h-4 bg-white/10 rounded animate-pulse w-full" />
        <div className="h-4 bg-white/10 rounded animate-pulse w-2/3" />
      </div>
      
      <div className="flex space-x-2">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="h-6 w-16 bg-white/10 rounded animate-pulse"
            style={{ animationDelay: `${index * 0.1}s` }}
          />
        ))}
      </div>
    </div>
  )
}

interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  className?: string
}

export function LoadingOverlay({ isLoading, children, className }: LoadingOverlayProps) {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <div className="flex flex-col items-center space-y-3">
            <LoadingSpinner size="lg" />
            <p className="text-white text-sm">Loading...</p>
          </div>
        </div>
      )}
    </div>
  )
}

interface LoadingDotsProps {
  className?: string
}

export function LoadingDots({ className }: LoadingDotsProps) {
  return (
    <div className={cn('flex space-x-1', className)}>
      {Array.from({ length: 3 }).map((_, index) => (
        <div
          key={index}
          className="w-2 h-2 bg-white rounded-full animate-bounce"
          style={{ animationDelay: `${index * 0.1}s` }}
        />
      ))}
    </div>
  )
}

interface LoadingPulseProps {
  className?: string
}

export function LoadingPulse({ className }: LoadingPulseProps) {
  return (
    <div className={cn('relative', className)}>
      <div className="w-4 h-4 bg-blue-400 rounded-full animate-ping absolute" />
      <div className="w-4 h-4 bg-blue-500 rounded-full" />
    </div>
  )
}

interface LoadingProgressProps {
  progress: number
  className?: string
  showPercentage?: boolean
}

export function LoadingProgress({ progress, className, showPercentage = true }: LoadingProgressProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <div className="w-full bg-white/20 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
        />
      </div>
      {showPercentage && (
        <div className="text-center text-sm text-white/60">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  )
}

interface LoadingStateProps {
  isLoading: boolean
  error?: string | null
  children: React.ReactNode
  loadingComponent?: React.ReactNode
  errorComponent?: React.ReactNode
  className?: string
}

export function LoadingState({
  isLoading,
  error,
  children,
  loadingComponent,
  errorComponent,
  className
}: LoadingStateProps) {
  if (error) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        {errorComponent || (
          <div className="text-center space-y-2">
            <div className="text-red-400 text-lg">⚠️ Error</div>
            <p className="text-white/60 text-sm">{error}</p>
          </div>
        )}
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        {loadingComponent || (
          <div className="flex flex-col items-center space-y-3">
            <LoadingSpinner size="lg" />
            <p className="text-white/60 text-sm">Loading...</p>
          </div>
        )}
      </div>
    )
  }

  return <>{children}</>
}
