'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  HelpCircle,
  Filter,
  Search
} from 'lucide-react'
import { IndicatorStatus, IndicatorType } from '@/types/indicators'

interface IndicatorData {
  id: string
  name: string
  type: IndicatorType
  status: IndicatorStatus
  value: number | string
  strength: number
  lastUpdate: Date
}

// Mock indicator data - In real app, this would come from calculations
const mockIndicators: IndicatorData[] = [
  // Momentum Indicators
  { id: 'RSI', name: 'RSI (14)', type: 'momentum', status: 'bullish', value: 35.2, strength: 78, lastUpdate: new Date() },
  { id: 'MACD', name: 'MACD (12,26,9)', type: 'momentum', status: 'bullish', value: 'Bullish Cross', strength: 85, lastUpdate: new Date() },
  { id: 'Stochastic', name: 'Stochastic %K', type: 'momentum', status: 'neutral', value: 52.1, strength: 45, lastUpdate: new Date() },
  { id: '<PERSON>', name: '<PERSON> %R', type: 'momentum', status: 'bullish', value: -25.3, strength: 72, lastUpdate: new Date() },
  { id: 'CCI', name: 'CCI (20)', type: 'momentum', status: 'bearish', value: -120.5, strength: 68, lastUpdate: new Date() },
  
  // Trend Indicators
  { id: 'SMA20', name: 'SMA (20)', type: 'trend', status: 'bullish', value: 51850, strength: 65, lastUpdate: new Date() },
  { id: 'EMA50', name: 'EMA (50)', type: 'trend', status: 'bullish', value: 51200, strength: 70, lastUpdate: new Date() },
  { id: 'ParabolicSAR', name: 'Parabolic SAR', type: 'trend', status: 'bullish', value: 50800, strength: 82, lastUpdate: new Date() },
  { id: 'Ichimoku', name: 'Ichimoku Cloud', type: 'trend', status: 'bullish', value: 'Above Cloud', strength: 88, lastUpdate: new Date() },
  { id: 'ADX', name: 'ADX (14)', type: 'trend', status: 'neutral', value: 28.5, strength: 55, lastUpdate: new Date() },
  
  // Volatility Indicators
  { id: 'BollingerBands', name: 'Bollinger Bands', type: 'volatility', status: 'neutral', value: 'Mid-Band', strength: 40, lastUpdate: new Date() },
  { id: 'ATR', name: 'ATR (14)', type: 'volatility', status: 'neutral', value: 1250, strength: 60, lastUpdate: new Date() },
  { id: 'Keltner', name: 'Keltner Channel', type: 'volatility', status: 'bullish', value: 'Above Mid', strength: 75, lastUpdate: new Date() },
  
  // Volume Indicators
  { id: 'Volume', name: 'Volume', type: 'volume', status: 'bullish', value: '125% Avg', strength: 80, lastUpdate: new Date() },
  { id: 'VWAP', name: 'VWAP', type: 'volume', status: 'bullish', value: 51900, strength: 73, lastUpdate: new Date() },
  { id: 'MFI', name: 'MFI (14)', type: 'volume', status: 'neutral', value: 58.2, strength: 48, lastUpdate: new Date() },
  { id: 'OBV', name: 'On Balance Volume', type: 'volume', status: 'bullish', value: 'Rising', strength: 77, lastUpdate: new Date() },
  
  // Support/Resistance
  { id: 'Support1', name: 'Support Level 1', type: 'support_resistance', status: 'bullish', value: 50800, strength: 85, lastUpdate: new Date() },
  { id: 'Resistance1', name: 'Resistance Level 1', type: 'support_resistance', status: 'neutral', value: 54200, strength: 60, lastUpdate: new Date() },
  { id: 'Pivot', name: 'Pivot Point', type: 'support_resistance', status: 'bullish', value: 52100, strength: 70, lastUpdate: new Date() },
  
  // Macro Indicators
  { id: 'SP500', name: 'S&P 500', type: 'macro', status: 'bullish', value: '+0.8%', strength: 65, lastUpdate: new Date() },
  { id: 'DXY', name: 'DXY Index', type: 'macro', status: 'bearish', value: 104.2, strength: 58, lastUpdate: new Date() },
  { id: 'VIX', name: 'VIX', type: 'macro', status: 'bullish', value: 18.3, strength: 72, lastUpdate: new Date() },
  { id: 'BTCD', name: 'BTC Dominance', type: 'macro', status: 'neutral', value: '52.4%', strength: 50, lastUpdate: new Date() },
  
  // On-Chain
  { id: 'ExchangeFlow', name: 'Exchange Flow', type: 'on_chain', status: 'bullish', value: 'Outflow', strength: 78, lastUpdate: new Date() },
  { id: 'WhaleActivity', name: 'Whale Activity', type: 'on_chain', status: 'neutral', value: 'Normal', strength: 45, lastUpdate: new Date() },
  { id: 'NetworkHealth', name: 'Network Health', type: 'on_chain', status: 'bullish', value: 'Strong', strength: 82, lastUpdate: new Date() },
  
  // Sentiment
  { id: 'FearGreed', name: 'Fear & Greed', type: 'sentiment', status: 'bullish', value: 65, strength: 65, lastUpdate: new Date() },
  { id: 'FundingRate', name: 'Funding Rate', type: 'sentiment', status: 'neutral', value: '0.01%', strength: 40, lastUpdate: new Date() },
  { id: 'OpenInterest', name: 'Open Interest', type: 'sentiment', status: 'bullish', value: 'Rising', strength: 70, lastUpdate: new Date() }
]

export function IndicatorMatrix() {
  const [selectedType, setSelectedType] = useState<IndicatorType | 'all'>('all')
  const [searchTerm, setSearchTerm] = useState('')

  const getStatusIcon = (status: IndicatorStatus) => {
    switch (status) {
      case 'bullish':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'bearish':
        return <TrendingDown className="h-4 w-4 text-red-400" />
      case 'neutral':
        return <Minus className="h-4 w-4 text-yellow-400" />
      default:
        return <HelpCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: IndicatorStatus) => {
    switch (status) {
      case 'bullish':
        return 'bg-green-400/20 border-green-400/30'
      case 'bearish':
        return 'bg-red-400/20 border-red-400/30'
      case 'neutral':
        return 'bg-yellow-400/20 border-yellow-400/30'
      default:
        return 'bg-gray-400/20 border-gray-400/30'
    }
  }

  const getStrengthColor = (strength: number) => {
    if (strength >= 80) return 'bg-green-400'
    if (strength >= 60) return 'bg-yellow-400'
    if (strength >= 40) return 'bg-orange-400'
    return 'bg-red-400'
  }

  const filteredIndicators = mockIndicators.filter(indicator => {
    const matchesType = selectedType === 'all' || indicator.type === selectedType
    const matchesSearch = indicator.name.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesType && matchesSearch
  })

  const indicatorTypes: { value: IndicatorType | 'all'; label: string }[] = [
    { value: 'all', label: 'All' },
    { value: 'momentum', label: 'Momentum' },
    { value: 'trend', label: 'Trend' },
    { value: 'volatility', label: 'Volatility' },
    { value: 'volume', label: 'Volume' },
    { value: 'support_resistance', label: 'S/R' },
    { value: 'macro', label: 'Macro' },
    { value: 'on_chain', label: 'On-Chain' },
    { value: 'sentiment', label: 'Sentiment' }
  ]

  const getTypeStats = () => {
    const stats = {
      bullish: filteredIndicators.filter(i => i.status === 'bullish').length,
      bearish: filteredIndicators.filter(i => i.status === 'bearish').length,
      neutral: filteredIndicators.filter(i => i.status === 'neutral').length,
      total: filteredIndicators.length
    }
    return stats
  }

  const stats = getTypeStats()

  return (
    <Card className="glass-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl font-bold text-white">
            Technical Indicators Matrix
          </CardTitle>
          <div className="flex items-center space-x-4">
            {/* Stats */}
            <div className="flex items-center space-x-2 text-sm">
              <Badge variant="success" className="text-xs">
                {stats.bullish} Bullish
              </Badge>
              <Badge variant="danger" className="text-xs">
                {stats.bearish} Bearish
              </Badge>
              <Badge variant="warning" className="text-xs">
                {stats.neutral} Neutral
              </Badge>
            </div>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex items-center space-x-4 mt-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-white/60" />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value as IndicatorType | 'all')}
              className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-400"
            >
              {indicatorTypes.map(type => (
                <option key={type.value} value={type.value} className="bg-slate-800">
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-2 flex-1 max-w-xs">
            <Search className="h-4 w-4 text-white/60" />
            <input
              type="text"
              placeholder="Search indicators..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-white/10 border border-white/20 rounded-lg px-3 py-1 text-white text-sm placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-400 flex-1"
            />
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredIndicators.map((indicator) => (
            <div
              key={indicator.id}
              className={`p-4 rounded-lg border transition-all duration-200 hover:scale-105 cursor-pointer ${getStatusColor(indicator.status)}`}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(indicator.status)}
                  <span className="text-sm font-medium text-white">
                    {indicator.name}
                  </span>
                </div>
                <Badge variant="outline" className="text-xs border-white/20 text-white/70">
                  {indicator.type}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="text-lg font-bold text-white">
                  {typeof indicator.value === 'number' 
                    ? indicator.value.toLocaleString() 
                    : indicator.value
                  }
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-white/60">Strength:</span>
                  <div className="flex-1 h-2 bg-white/20 rounded-full">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor(indicator.strength)}`}
                      style={{ width: `${indicator.strength}%` }}
                    />
                  </div>
                  <span className="text-xs text-white font-medium">
                    {indicator.strength}%
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredIndicators.length === 0 && (
          <div className="text-center py-12">
            <p className="text-white/60">No indicators found matching your criteria.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
