import { IndicatorValue } from '@/types/indicators'
import { calculateSMA, calculateEMA } from './technical'

// Parabolic SAR
export function calculateParabolicSAR(
  high: number[],
  low: number[],
  close: number[],
  step: number = 0.02,
  maxStep: number = 0.2
): IndicatorValue[] {
  const result: IndicatorValue[] = []
  let trend = 1 // 1 for uptrend, -1 for downtrend
  let sar = low[0]
  let ep = high[0] // Extreme Point
  let af = step // Acceleration Factor
  
  for (let i = 1; i < close.length; i++) {
    // Calculate new SAR
    sar = sar + af * (ep - sar)
    
    if (trend === 1) { // Uptrend
      if (low[i] <= sar) {
        // Trend reversal to downtrend
        trend = -1
        sar = ep
        ep = low[i]
        af = step
      } else {
        if (high[i] > ep) {
          ep = high[i]
          af = Math.min(af + step, maxStep)
        }
      }
    } else { // Downtrend
      if (high[i] >= sar) {
        // Trend reversal to uptrend
        trend = 1
        sar = ep
        ep = high[i]
        af = step
      } else {
        if (low[i] < ep) {
          ep = low[i]
          af = Math.min(af + step, maxStep)
        }
      }
    }
    
    const status = trend === 1 ? 'bullish' : 'bearish'
    const strength = Math.abs(close[i] - sar) / close[i] * 100
    
    result.push({
      value: sar,
      status,
      strength: Math.min(strength * 10, 100),
      timestamp: new Date(),
      metadata: {
        trend: trend === 1 ? 'up' : 'down',
        accelerationFactor: af,
        extremePoint: ep
      }
    })
  }
  
  return result
}

// Ichimoku Cloud
export function calculateIchimoku(
  high: number[],
  low: number[],
  close: number[],
  tenkanPeriod: number = 9,
  kijunPeriod: number = 26,
  senkouBPeriod: number = 52
): IndicatorValue[] {
  const result: IndicatorValue[] = []
  
  for (let i = Math.max(tenkanPeriod, kijunPeriod) - 1; i < close.length; i++) {
    // Tenkan-sen (Conversion Line)
    const tenkanHigh = Math.max(...high.slice(i - tenkanPeriod + 1, i + 1))
    const tenkanLow = Math.min(...low.slice(i - tenkanPeriod + 1, i + 1))
    const tenkanSen = (tenkanHigh + tenkanLow) / 2
    
    // Kijun-sen (Base Line)
    const kijunHigh = Math.max(...high.slice(i - kijunPeriod + 1, i + 1))
    const kijunLow = Math.min(...low.slice(i - kijunPeriod + 1, i + 1))
    const kijunSen = (kijunHigh + kijunLow) / 2
    
    // Senkou Span A (Leading Span A)
    const senkouSpanA = (tenkanSen + kijunSen) / 2
    
    // Senkou Span B (Leading Span B)
    let senkouSpanB = 0
    if (i >= senkouBPeriod - 1) {
      const senkouBHigh = Math.max(...high.slice(i - senkouBPeriod + 1, i + 1))
      const senkouBLow = Math.min(...low.slice(i - senkouBPeriod + 1, i + 1))
      senkouSpanB = (senkouBHigh + senkouBLow) / 2
    }
    
    // Chikou Span (Lagging Span) - current close shifted back
    const chikouSpan = close[i]
    
    // Determine cloud status
    const cloudTop = Math.max(senkouSpanA, senkouSpanB)
    const cloudBottom = Math.min(senkouSpanA, senkouSpanB)
    const priceAboveCloud = close[i] > cloudTop
    const priceBelowCloud = close[i] < cloudBottom
    const priceInCloud = !priceAboveCloud && !priceBelowCloud
    
    let status: 'bullish' | 'bearish' | 'neutral' = 'neutral'
    if (priceAboveCloud && tenkanSen > kijunSen) status = 'bullish'
    else if (priceBelowCloud && tenkanSen < kijunSen) status = 'bearish'
    
    const strength = priceInCloud ? 25 : Math.abs(close[i] - (cloudTop + cloudBottom) / 2) / close[i] * 100
    
    result.push({
      value: {
        tenkanSen,
        kijunSen,
        senkouSpanA,
        senkouSpanB,
        chikouSpan,
        cloudTop,
        cloudBottom
      },
      status,
      strength: Math.min(strength * 5, 100),
      timestamp: new Date(),
      metadata: {
        priceAboveCloud,
        priceBelowCloud,
        priceInCloud,
        tkCross: tenkanSen > kijunSen ? 'bullish' : 'bearish',
        cloudColor: senkouSpanA > senkouSpanB ? 'green' : 'red'
      }
    })
  }
  
  return result
}

// Volume Weighted Average Price (VWAP)
export function calculateVWAP(
  high: number[],
  low: number[],
  close: number[],
  volume: number[]
): IndicatorValue[] {
  const result: IndicatorValue[] = []
  let cumulativeTPV = 0 // Typical Price * Volume
  let cumulativeVolume = 0
  
  for (let i = 0; i < close.length; i++) {
    const typicalPrice = (high[i] + low[i] + close[i]) / 3
    const tpv = typicalPrice * volume[i]
    
    cumulativeTPV += tpv
    cumulativeVolume += volume[i]
    
    const vwap = cumulativeTPV / cumulativeVolume
    const deviation = Math.abs(close[i] - vwap) / vwap * 100
    
    let status: 'bullish' | 'bearish' | 'neutral' = 'neutral'
    if (close[i] > vwap) status = 'bullish'
    else if (close[i] < vwap) status = 'bearish'
    
    result.push({
      value: vwap,
      status,
      strength: Math.min(deviation * 10, 100),
      timestamp: new Date(),
      metadata: {
        deviation,
        priceAboveVWAP: close[i] > vwap,
        volumeWeightedPrice: typicalPrice
      }
    })
  }
  
  return result
}

// Money Flow Index (MFI)
export function calculateMFI(
  high: number[],
  low: number[],
  close: number[],
  volume: number[],
  period: number = 14
): IndicatorValue[] {
  const result: IndicatorValue[] = []
  const typicalPrices: number[] = []
  const rawMoneyFlows: number[] = []
  
  // Calculate Typical Price and Raw Money Flow
  for (let i = 0; i < close.length; i++) {
    const typicalPrice = (high[i] + low[i] + close[i]) / 3
    typicalPrices.push(typicalPrice)
    rawMoneyFlows.push(typicalPrice * volume[i])
  }
  
  for (let i = period; i < close.length; i++) {
    let positiveMoneyFlow = 0
    let negativeMoneyFlow = 0
    
    for (let j = i - period + 1; j <= i; j++) {
      if (typicalPrices[j] > typicalPrices[j - 1]) {
        positiveMoneyFlow += rawMoneyFlows[j]
      } else if (typicalPrices[j] < typicalPrices[j - 1]) {
        negativeMoneyFlow += rawMoneyFlows[j]
      }
    }
    
    const moneyFlowRatio = positiveMoneyFlow / (negativeMoneyFlow || 1)
    const mfi = 100 - (100 / (1 + moneyFlowRatio))
    
    let status: 'bullish' | 'bearish' | 'neutral' = 'neutral'
    if (mfi > 80) status = 'bearish' // Overbought
    else if (mfi < 20) status = 'bullish' // Oversold
    
    result.push({
      value: mfi,
      status,
      strength: Math.abs(mfi - 50) * 2,
      timestamp: new Date(),
      metadata: {
        overbought: mfi > 80,
        oversold: mfi < 20,
        moneyFlowRatio,
        positiveMoneyFlow,
        negativeMoneyFlow
      }
    })
  }
  
  return result
}
