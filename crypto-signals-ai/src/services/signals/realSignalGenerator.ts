import { Signal, SignalType, SignalStrength, SignalPriority } from '@/types/signals'
import { MarketData } from '@/types/market'
import { marketDataService } from '@/services/api/marketDataService'
import { cryptoMetricsService } from '@/services/api/cryptoMetricsService'

/**
 * Real Signal Generator
 * Generates trading signals based on real market data and technical indicators
 */
export class RealSignalGenerator {
  
  // Generate signals for a specific symbol using real market data
  async generateSignals(symbol: string, timeframe: string, historicalData: MarketData[]): Promise<Signal[]> {
    if (historicalData.length < 20) {
      return [] // Need at least 20 data points for analysis
    }

    const signals: Signal[] = []
    const currentPrice = historicalData[historicalData.length - 1].close
    
    try {
      // Calculate technical indicators from real data
      const indicators = this.calculateIndicators(historicalData)
      
      // Get real market context
      const marketContext = await this.getMarketContext(symbol)
      
      // Generate different types of signals based on real analysis
      const buySignal = this.generateBuySignal(symbol, timeframe, currentPrice, indicators, marketContext)
      const sellSignal = this.generateSellSignal(symbol, timeframe, currentPrice, indicators, marketContext)
      const alertSignal = this.generateAlertSignal(symbol, timeframe, currentPrice, indicators, marketContext)
      
      if (buySignal) signals.push(buySignal)
      if (sellSignal) signals.push(sellSignal)
      if (alertSignal) signals.push(alertSignal)
      
    } catch (error) {
      console.error(`Failed to generate real signals for ${symbol}:`, error)
    }
    
    return signals
  }

  // Calculate real technical indicators from historical data
  private calculateIndicators(data: MarketData[]) {
    const closes = data.map(d => d.close)
    const volumes = data.map(d => d.volume)
    const highs = data.map(d => d.high)
    const lows = data.map(d => d.low)
    
    return {
      rsi: this.calculateRSI(closes, 14),
      macd: this.calculateMACD(closes),
      sma20: this.calculateSMA(closes, 20),
      sma50: this.calculateSMA(closes, 50),
      volume: this.analyzeVolume(volumes),
      volatility: this.calculateVolatility(closes),
      support: this.findSupport(lows),
      resistance: this.findResistance(highs),
      trend: this.determineTrend(closes),
      bollingerBands: this.calculateBollingerBands(closes, 20, 2)
    }
  }

  // Get real market context from APIs
  private async getMarketContext(symbol: string) {
    try {
      const [metrics, marketOverview] = await Promise.all([
        cryptoMetricsService.getAllMetrics(),
        marketDataService.getMarketOverview()
      ])
      
      return {
        fundingRates: metrics.fundingRates.filter(f => f.symbol === symbol.replace('/', '')),
        fearGreedIndex: marketOverview.fearGreedIndex,
        btcDominance: marketOverview.btcDominance,
        marketTrend: marketOverview.marketCapChange24h > 0 ? 'bullish' : 'bearish',
        onChainMetrics: metrics.onChainMetrics
      }
    } catch (error) {
      console.warn('Failed to get market context:', error)
      return {
        fundingRates: [],
        fearGreedIndex: 50,
        btcDominance: 50,
        marketTrend: 'neutral' as const,
        onChainMetrics: []
      }
    }
  }

  // Generate buy signal based on real technical analysis
  private generateBuySignal(
    symbol: string, 
    timeframe: string, 
    currentPrice: number, 
    indicators: any, 
    context: any
  ): Signal | null {
    let score = 0
    let confidence = 0
    const reasoning: string[] = []
    const signalIndicators: any[] = []

    // RSI oversold condition (strong buy signal)
    if (indicators.rsi < 30) {
      score += 25
      confidence += 20
      reasoning.push(`RSI oversold at ${indicators.rsi.toFixed(1)} - strong reversal signal`)
      signalIndicators.push({
        id: 'RSI',
        name: 'RSI',
        type: 'momentum',
        value: indicators.rsi,
        weight: 0.25,
        status: 'bullish',
        confirmation: true
      })
    } else if (indicators.rsi < 40) {
      score += 15
      confidence += 10
      reasoning.push(`RSI at ${indicators.rsi.toFixed(1)} - potential buy zone`)
    }

    // MACD bullish crossover
    if (indicators.macd.histogram > 0 && indicators.macd.macd > indicators.macd.signal) {
      score += 20
      confidence += 15
      reasoning.push('MACD bullish crossover with positive momentum')
      signalIndicators.push({
        id: 'MACD',
        name: 'MACD',
        type: 'momentum',
        value: indicators.macd,
        weight: 0.20,
        status: 'bullish',
        confirmation: true
      })
    }

    // Price above moving averages
    if (currentPrice > indicators.sma20 && indicators.sma20 > indicators.sma50) {
      score += 15
      confidence += 10
      reasoning.push('Price above 20 SMA and 20 SMA above 50 SMA - uptrend confirmed')
    }

    // Bollinger Bands analysis
    if (currentPrice < indicators.bollingerBands.lower) {
      score += 15
      confidence += 10
      reasoning.push('Price below lower Bollinger Band - oversold condition')
    }

    // Volume confirmation
    if (indicators.volume.trend === 'increasing' && indicators.volume.ratio > 1.2) {
      score += 10
      confidence += 15
      reasoning.push(`Volume increasing by ${((indicators.volume.ratio - 1) * 100).toFixed(1)}% - confirming move`)
      signalIndicators.push({
        id: 'Volume',
        name: 'Volume',
        type: 'volume',
        value: indicators.volume.ratio,
        weight: 0.10,
        status: 'bullish',
        confirmation: true
      })
    }

    // Market sentiment analysis
    if (context.fearGreedIndex < 25) {
      score += 15
      confidence += 10
      reasoning.push(`Extreme fear at ${context.fearGreedIndex} - contrarian buy opportunity`)
    } else if (context.fearGreedIndex < 40) {
      score += 8
      confidence += 5
      reasoning.push(`Fear sentiment at ${context.fearGreedIndex} - potential buy zone`)
    }

    // Funding rates analysis (negative = shorts paying longs = bearish sentiment = buy opportunity)
    const avgFunding = context.fundingRates.reduce((sum: number, f: any) => sum + f.rate, 0) / Math.max(1, context.fundingRates.length)
    if (avgFunding < -0.01) {
      score += 10
      confidence += 8
      reasoning.push(`Negative funding rates at ${(avgFunding * 100).toFixed(3)}% - shorts paying longs`)
    }

    // Support level proximity
    const supportDistance = Math.abs(currentPrice - indicators.support) / currentPrice
    if (supportDistance < 0.02) {
      score += 12
      confidence += 8
      reasoning.push('Price near strong support level - good risk/reward setup')
    }

    // Only generate signal if score meets threshold
    if (score < 60) return null

    const strength: SignalStrength = score >= 85 ? 'strong' : score >= 70 ? 'moderate' : 'weak'
    const priority: SignalPriority = score >= 85 ? 'high' : score >= 70 ? 'medium' : 'low'

    // Calculate realistic targets based on volatility
    const atr = indicators.volatility * currentPrice
    const targetPrice = currentPrice + (atr * 2.5) // 2.5 ATR target
    const stopLoss = currentPrice - (atr * 1.2) // 1.2 ATR stop

    return {
      id: `buy_${symbol}_${Date.now()}`,
      symbol,
      type: 'buy',
      strength,
      priority,
      score,
      confidence,
      timeframe: timeframe as any,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours
      
      entryPrice: currentPrice,
      targetPrice,
      stopLoss,
      
      title: `${strength.charAt(0).toUpperCase() + strength.slice(1)} Buy Signal - ${symbol}`,
      description: `Real technical analysis shows ${confidence}% confidence buy setup for ${symbol}`,
      reasoning,
      
      indicators: signalIndicators,
      
      riskReward: (targetPrice - currentPrice) / (currentPrice - stopLoss),
      
      status: 'active',
      tags: ['buy', 'technical', 'real-data'],
      category: 'technical_analysis'
    }
  }

  // Generate sell signal (similar logic but inverted)
  private generateSellSignal(
    symbol: string, 
    timeframe: string, 
    currentPrice: number, 
    indicators: any, 
    context: any
  ): Signal | null {
    let score = 0
    let confidence = 0
    const reasoning: string[] = []
    const signalIndicators: any[] = []

    // RSI overbought condition
    if (indicators.rsi > 70) {
      score += 25
      confidence += 20
      reasoning.push(`RSI overbought at ${indicators.rsi.toFixed(1)} - strong reversal signal`)
      signalIndicators.push({
        id: 'RSI',
        name: 'RSI',
        type: 'momentum',
        value: indicators.rsi,
        weight: 0.25,
        status: 'bearish',
        confirmation: true
      })
    }

    // MACD bearish crossover
    if (indicators.macd.histogram < 0 && indicators.macd.macd < indicators.macd.signal) {
      score += 20
      confidence += 15
      reasoning.push('MACD bearish crossover with negative momentum')
    }

    // Price below moving averages
    if (currentPrice < indicators.sma20 && indicators.sma20 < indicators.sma50) {
      score += 15
      confidence += 10
      reasoning.push('Price below 20 SMA and 20 SMA below 50 SMA - downtrend confirmed')
    }

    // Bollinger Bands analysis
    if (currentPrice > indicators.bollingerBands.upper) {
      score += 15
      confidence += 10
      reasoning.push('Price above upper Bollinger Band - overbought condition')
    }

    // Market sentiment (extreme greed = sell signal)
    if (context.fearGreedIndex > 75) {
      score += 15
      confidence += 10
      reasoning.push(`Extreme greed at ${context.fearGreedIndex} - contrarian sell signal`)
    }

    // High positive funding rates
    const avgFunding = context.fundingRates.reduce((sum: number, f: any) => sum + f.rate, 0) / Math.max(1, context.fundingRates.length)
    if (avgFunding > 0.02) {
      score += 10
      confidence += 8
      reasoning.push(`High funding rates at ${(avgFunding * 100).toFixed(3)}% - longs paying shorts`)
    }

    // Resistance level proximity
    const resistanceDistance = Math.abs(currentPrice - indicators.resistance) / currentPrice
    if (resistanceDistance < 0.02) {
      score += 12
      confidence += 8
      reasoning.push('Price near strong resistance level - potential reversal zone')
    }

    if (score < 60) return null

    const strength: SignalStrength = score >= 85 ? 'strong' : score >= 70 ? 'moderate' : 'weak'
    const priority: SignalPriority = score >= 85 ? 'high' : score >= 70 ? 'medium' : 'low'

    const atr = indicators.volatility * currentPrice
    const targetPrice = currentPrice - (atr * 2.5)
    const stopLoss = currentPrice + (atr * 1.2)

    return {
      id: `sell_${symbol}_${Date.now()}`,
      symbol,
      type: 'sell',
      strength,
      priority,
      score,
      confidence,
      timeframe: timeframe as any,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + 4 * 60 * 60 * 1000),
      
      entryPrice: currentPrice,
      targetPrice,
      stopLoss,
      
      title: `${strength.charAt(0).toUpperCase() + strength.slice(1)} Sell Signal - ${symbol}`,
      description: `Real technical analysis shows ${confidence}% confidence sell setup for ${symbol}`,
      reasoning,
      
      indicators: signalIndicators,
      
      riskReward: (currentPrice - targetPrice) / (stopLoss - currentPrice),
      
      status: 'active',
      tags: ['sell', 'technical', 'real-data'],
      category: 'technical_analysis'
    }
  }

  // Generate alert signal for important market conditions
  private generateAlertSignal(
    symbol: string, 
    timeframe: string, 
    currentPrice: number, 
    indicators: any, 
    context: any
  ): Signal | null {
    let score = 0
    const reasoning: string[] = []

    // High volatility alert
    if (indicators.volatility > 0.05) {
      score += 30
      reasoning.push(`High volatility detected: ${(indicators.volatility * 100).toFixed(1)}%`)
    }

    // Support/resistance level test
    const supportDistance = Math.abs(currentPrice - indicators.support) / currentPrice
    const resistanceDistance = Math.abs(currentPrice - indicators.resistance) / currentPrice
    
    if (supportDistance < 0.015) {
      score += 25
      reasoning.push(`Price testing key support at ${indicators.support.toFixed(2)}`)
    }
    
    if (resistanceDistance < 0.015) {
      score += 25
      reasoning.push(`Price testing key resistance at ${indicators.resistance.toFixed(2)}`)
    }

    // Extreme funding rates
    const avgFunding = context.fundingRates.reduce((sum: number, f: any) => sum + f.rate, 0) / Math.max(1, context.fundingRates.length)
    if (Math.abs(avgFunding) > 0.05) {
      score += 20
      reasoning.push(`Extreme funding rates: ${(avgFunding * 100).toFixed(3)}%`)
    }

    // Extreme fear/greed
    if (context.fearGreedIndex < 20 || context.fearGreedIndex > 80) {
      score += 15
      reasoning.push(`Extreme market sentiment: ${context.fearGreedIndex}`)
    }

    if (score < 50) return null

    const strength: SignalStrength = score >= 80 ? 'strong' : score >= 65 ? 'moderate' : 'weak'
    const priority: SignalPriority = score >= 80 ? 'critical' : score >= 65 ? 'high' : 'medium'

    return {
      id: `alert_${symbol}_${Date.now()}`,
      symbol,
      type: 'alert',
      strength,
      priority,
      score,
      confidence: score,
      timeframe: timeframe as any,
      timestamp: new Date(),
      expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
      
      title: `Market Alert - ${symbol}`,
      description: `Important market conditions detected for ${symbol} based on real data`,
      reasoning,
      
      indicators: [],
      
      status: 'active',
      tags: ['alert', 'real-data', 'market-structure'],
      category: 'market_structure'
    }
  }

  // Real technical indicator calculations
  private calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50

    let gains = 0
    let losses = 0

    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1]
      if (change > 0) gains += change
      else losses -= change
    }

    const avgGain = gains / period
    const avgLoss = losses / period
    
    if (avgLoss === 0) return 100
    
    const rs = avgGain / avgLoss
    return 100 - (100 / (1 + rs))
  }

  private calculateMACD(prices: number[]) {
    const ema12 = this.calculateEMA(prices, 12)
    const ema26 = this.calculateEMA(prices, 26)
    const macd = ema12 - ema26
    const signal = this.calculateEMA([macd], 9)
    const histogram = macd - signal

    return { macd, signal, histogram }
  }

  private calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return prices[prices.length - 1] || 0
    const sum = prices.slice(-period).reduce((a, b) => a + b, 0)
    return sum / period
  }

  private calculateEMA(prices: number[], period: number): number {
    if (prices.length === 0) return 0
    if (prices.length === 1) return prices[0]
    
    const multiplier = 2 / (period + 1)
    let ema = prices[0]
    
    for (let i = 1; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
    }
    
    return ema
  }

  private calculateBollingerBands(prices: number[], period: number, stdDev: number) {
    const sma = this.calculateSMA(prices, period)
    const recentPrices = prices.slice(-period)
    
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period
    const standardDeviation = Math.sqrt(variance)
    
    return {
      upper: sma + (standardDeviation * stdDev),
      middle: sma,
      lower: sma - (standardDeviation * stdDev)
    }
  }

  private analyzeVolume(volumes: number[]) {
    if (volumes.length < 10) return { trend: 'neutral', ratio: 1 }
    
    const recent = volumes.slice(-5)
    const previous = volumes.slice(-10, -5)
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const previousAvg = previous.reduce((a, b) => a + b, 0) / previous.length
    
    return {
      trend: recentAvg > previousAvg ? 'increasing' : 'decreasing',
      ratio: previousAvg > 0 ? recentAvg / previousAvg : 1
    }
  }

  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0
    
    const returns = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    
    const mean = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length
    return Math.sqrt(variance)
  }

  private findSupport(lows: number[]): number {
    if (lows.length === 0) return 0
    return Math.min(...lows.slice(-20)) // Lowest low in last 20 periods
  }

  private findResistance(highs: number[]): number {
    if (highs.length === 0) return 0
    return Math.max(...highs.slice(-20)) // Highest high in last 20 periods
  }

  private determineTrend(prices: number[]): 'bullish' | 'bearish' | 'neutral' {
    if (prices.length < 10) return 'neutral'
    
    const recent = prices.slice(-5)
    const previous = prices.slice(-10, -5)
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length
    const previousAvg = previous.reduce((a, b) => a + b, 0) / previous.length
    
    const change = (recentAvg - previousAvg) / previousAvg
    
    if (change > 0.02) return 'bullish'
    if (change < -0.02) return 'bearish'
    return 'neutral'
  }
}
