import { Signal, SignalType, SignalStrength, SignalPriority, SignalIndicator } from '@/types/signals'
import { IndicatorValue, IndicatorStatus } from '@/types/indicators'
import { MarketData, TimeFrame } from '@/types/market'

export class SignalGenerator {
  private indicators: Map<string, IndicatorValue[]> = new Map()
  private weights: Map<string, number> = new Map()
  
  constructor() {
    this.initializeWeights()
  }
  
  private initializeWeights() {
    // Set weights for different indicators (0-1 scale)
    this.weights.set('RSI', 0.15)
    this.weights.set('MACD', 0.20)
    this.weights.set('BollingerBands', 0.15)
    this.weights.set('Stochastic', 0.10)
    this.weights.set('ATR', 0.05)
    this.weights.set('ParabolicSAR', 0.10)
    this.weights.set('Ichimoku', 0.15)
    this.weights.set('VWAP', 0.10)
    this.weights.set('MFI', 0.10)
    this.weights.set('Volume', 0.08)
    this.weights.set('SupportResistance', 0.12)
    this.weights.set('TrendLine', 0.08)
    this.weights.set('MacroSentiment', 0.15)
    this.weights.set('OnChain', 0.12)
  }
  
  public addIndicatorData(indicatorId: string, data: IndicatorValue[]) {
    this.indicators.set(indicatorId, data)
  }
  
  public generateSignals(
    symbol: string,
    timeframe: TimeFrame,
    marketData: MarketData[]
  ): Signal[] {
    const signals: Signal[] = []
    
    if (marketData.length === 0) return signals
    
    const currentPrice = marketData[marketData.length - 1].close
    const timestamp = new Date()
    
    // Generate different types of signals
    const buySignal = this.generateBuySignal(symbol, timeframe, currentPrice, timestamp)
    const sellSignal = this.generateSellSignal(symbol, timeframe, currentPrice, timestamp)
    const alertSignals = this.generateAlertSignals(symbol, timeframe, currentPrice, timestamp)
    
    if (buySignal) signals.push(buySignal)
    if (sellSignal) signals.push(sellSignal)
    signals.push(...alertSignals)
    
    return signals.filter(signal => signal.score >= 40) // Minimum score threshold
  }
  
  private generateBuySignal(
    symbol: string,
    timeframe: TimeFrame,
    currentPrice: number,
    timestamp: Date
  ): Signal | null {
    const indicators = this.getRelevantIndicators('buy')
    const score = this.calculateSignalScore(indicators, 'buy')
    
    if (score < 60) return null // Minimum buy signal threshold
    
    const strength = this.getSignalStrength(score)
    const priority = this.getSignalPriority(score, strength)
    const confidence = this.calculateConfidence(indicators)
    
    return {
      id: `${symbol}_${timeframe}_buy_${timestamp.getTime()}`,
      symbol,
      type: 'buy',
      strength,
      priority,
      score,
      confidence,
      timeframe,
      timestamp,
      expiresAt: new Date(timestamp.getTime() + this.getExpirationTime(timeframe)),
      
      entryPrice: currentPrice,
      targetPrice: this.calculateTargetPrice(currentPrice, indicators, 'buy'),
      stopLoss: this.calculateStopLoss(currentPrice, indicators, 'buy'),
      
      title: `Strong Buy Signal - ${symbol}`,
      description: `Multiple indicators confirm bullish momentum for ${symbol}`,
      reasoning: this.generateReasoning(indicators, 'buy'),
      
      indicators: this.formatSignalIndicators(indicators),
      
      riskReward: this.calculateRiskReward(currentPrice, indicators, 'buy'),
      
      status: 'active',
      tags: this.generateTags(indicators, 'buy'),
      category: 'technical_analysis'
    }
  }
  
  private generateSellSignal(
    symbol: string,
    timeframe: TimeFrame,
    currentPrice: number,
    timestamp: Date
  ): Signal | null {
    const indicators = this.getRelevantIndicators('sell')
    const score = this.calculateSignalScore(indicators, 'sell')
    
    if (score < 60) return null // Minimum sell signal threshold
    
    const strength = this.getSignalStrength(score)
    const priority = this.getSignalPriority(score, strength)
    const confidence = this.calculateConfidence(indicators)
    
    return {
      id: `${symbol}_${timeframe}_sell_${timestamp.getTime()}`,
      symbol,
      type: 'sell',
      strength,
      priority,
      score,
      confidence,
      timeframe,
      timestamp,
      expiresAt: new Date(timestamp.getTime() + this.getExpirationTime(timeframe)),
      
      entryPrice: currentPrice,
      targetPrice: this.calculateTargetPrice(currentPrice, indicators, 'sell'),
      stopLoss: this.calculateStopLoss(currentPrice, indicators, 'sell'),
      
      title: `Strong Sell Signal - ${symbol}`,
      description: `Multiple indicators confirm bearish momentum for ${symbol}`,
      reasoning: this.generateReasoning(indicators, 'sell'),
      
      indicators: this.formatSignalIndicators(indicators),
      
      riskReward: this.calculateRiskReward(currentPrice, indicators, 'sell'),
      
      status: 'active',
      tags: this.generateTags(indicators, 'sell'),
      category: 'technical_analysis'
    }
  }
  
  private generateAlertSignals(
    symbol: string,
    timeframe: TimeFrame,
    currentPrice: number,
    timestamp: Date
  ): Signal[] {
    const alerts: Signal[] = []
    
    // Volatility alert
    const atrData = this.indicators.get('ATR')
    if (atrData && atrData.length > 0) {
      const currentATR = atrData[atrData.length - 1]
      if (currentATR.metadata?.volatility === 'high') {
        alerts.push(this.createVolatilityAlert(symbol, timeframe, currentPrice, timestamp, currentATR))
      }
    }
    
    // Support/Resistance break alert
    const srData = this.indicators.get('SupportResistance')
    if (srData && srData.length > 0) {
      const currentSR = srData[srData.length - 1]
      if (currentSR.metadata?.breakout) {
        alerts.push(this.createBreakoutAlert(symbol, timeframe, currentPrice, timestamp, currentSR))
      }
    }
    
    return alerts
  }
  
  private getRelevantIndicators(signalType: 'buy' | 'sell'): Map<string, IndicatorValue> {
    const relevant = new Map<string, IndicatorValue>()
    
    for (const [indicatorId, data] of this.indicators) {
      if (data.length === 0) continue
      
      const currentValue = data[data.length - 1]
      const isRelevant = this.isIndicatorRelevant(currentValue, signalType)
      
      if (isRelevant) {
        relevant.set(indicatorId, currentValue)
      }
    }
    
    return relevant
  }
  
  private isIndicatorRelevant(indicator: IndicatorValue, signalType: 'buy' | 'sell'): boolean {
    if (signalType === 'buy') {
      return indicator.status === 'bullish' || 
             (indicator.status === 'neutral' && indicator.strength > 50)
    } else {
      return indicator.status === 'bearish' || 
             (indicator.status === 'neutral' && indicator.strength > 50)
    }
  }
  
  private calculateSignalScore(indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): number {
    let totalScore = 0
    let totalWeight = 0
    
    for (const [indicatorId, indicator] of indicators) {
      const weight = this.weights.get(indicatorId) || 0.05
      let indicatorScore = 0
      
      if (signalType === 'buy' && indicator.status === 'bullish') {
        indicatorScore = indicator.strength
      } else if (signalType === 'sell' && indicator.status === 'bearish') {
        indicatorScore = indicator.strength
      } else if (indicator.status === 'neutral') {
        indicatorScore = indicator.strength * 0.5 // Reduced weight for neutral
      }
      
      totalScore += indicatorScore * weight
      totalWeight += weight
    }
    
    return totalWeight > 0 ? (totalScore / totalWeight) : 0
  }
  
  private getSignalStrength(score: number): SignalStrength {
    if (score >= 80) return 'strong'
    if (score >= 60) return 'moderate'
    return 'weak'
  }
  
  private getSignalPriority(score: number, strength: SignalStrength): SignalPriority {
    if (score >= 90) return 'critical'
    if (score >= 80) return 'high'
    if (score >= 60) return 'medium'
    return 'low'
  }
  
  private calculateConfidence(indicators: Map<string, IndicatorValue>): number {
    const confirmingIndicators = Array.from(indicators.values())
      .filter(ind => ind.status !== 'neutral').length
    
    const totalIndicators = indicators.size
    return totalIndicators > 0 ? (confirmingIndicators / totalIndicators) * 100 : 0
  }
  
  private generateReasoning(indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): string[] {
    const reasoning: string[] = []
    
    for (const [indicatorId, indicator] of indicators) {
      if (indicator.status === (signalType === 'buy' ? 'bullish' : 'bearish')) {
        reasoning.push(`${indicatorId} shows ${indicator.status} signal with ${indicator.strength.toFixed(1)}% strength`)
      }
    }
    
    return reasoning
  }
  
  private formatSignalIndicators(indicators: Map<string, IndicatorValue>): SignalIndicator[] {
    return Array.from(indicators.entries()).map(([id, indicator]) => ({
      id,
      name: id,
      type: this.getIndicatorType(id),
      value: indicator.value,
      weight: this.weights.get(id) || 0.05,
      status: indicator.status,
      confirmation: indicator.status !== 'neutral'
    }))
  }
  
  private getIndicatorType(indicatorId: string): any {
    // Map indicator IDs to types
    const typeMap: Record<string, string> = {
      'RSI': 'momentum',
      'MACD': 'momentum',
      'BollingerBands': 'volatility',
      'Stochastic': 'momentum',
      'ATR': 'volatility',
      'ParabolicSAR': 'trend',
      'Ichimoku': 'trend',
      'VWAP': 'volume',
      'MFI': 'volume'
    }
    
    return typeMap[indicatorId] || 'trend'
  }
  
  private calculateTargetPrice(currentPrice: number, indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): number {
    // Simple target calculation - can be enhanced with more sophisticated logic
    const atrData = this.indicators.get('ATR')
    const atr = atrData && atrData.length > 0 ? atrData[atrData.length - 1].value as number : currentPrice * 0.02
    
    if (signalType === 'buy') {
      return currentPrice + (atr * 2) // 2 ATR target
    } else {
      return currentPrice - (atr * 2) // 2 ATR target
    }
  }
  
  private calculateStopLoss(currentPrice: number, indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): number {
    // Simple stop loss calculation
    const atrData = this.indicators.get('ATR')
    const atr = atrData && atrData.length > 0 ? atrData[atrData.length - 1].value as number : currentPrice * 0.01
    
    if (signalType === 'buy') {
      return currentPrice - atr // 1 ATR stop loss
    } else {
      return currentPrice + atr // 1 ATR stop loss
    }
  }
  
  private calculateRiskReward(currentPrice: number, indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): number {
    const target = this.calculateTargetPrice(currentPrice, indicators, signalType)
    const stopLoss = this.calculateStopLoss(currentPrice, indicators, signalType)
    
    const reward = Math.abs(target - currentPrice)
    const risk = Math.abs(currentPrice - stopLoss)
    
    return risk > 0 ? reward / risk : 0
  }
  
  private getExpirationTime(timeframe: TimeFrame): number {
    // Expiration time in milliseconds based on timeframe
    const timeframes: Record<TimeFrame, number> = {
      '1m': 5 * 60 * 1000,      // 5 minutes
      '5m': 30 * 60 * 1000,     // 30 minutes
      '15m': 2 * 60 * 60 * 1000, // 2 hours
      '30m': 4 * 60 * 60 * 1000, // 4 hours
      '1h': 8 * 60 * 60 * 1000,  // 8 hours
      '4h': 24 * 60 * 60 * 1000, // 24 hours
      '1d': 7 * 24 * 60 * 60 * 1000, // 7 days
      '1w': 30 * 24 * 60 * 60 * 1000, // 30 days
      '1M': 90 * 24 * 60 * 60 * 1000  // 90 days
    }
    
    return timeframes[timeframe] || 60 * 60 * 1000 // Default 1 hour
  }
  
  private generateTags(indicators: Map<string, IndicatorValue>, signalType: 'buy' | 'sell'): string[] {
    const tags = [signalType, 'technical']
    
    // Add indicator-specific tags
    if (indicators.has('RSI')) tags.push('momentum')
    if (indicators.has('BollingerBands')) tags.push('volatility')
    if (indicators.has('VWAP')) tags.push('volume')
    if (indicators.has('Ichimoku')) tags.push('trend')
    
    return tags
  }
  
  private createVolatilityAlert(
    symbol: string,
    timeframe: TimeFrame,
    currentPrice: number,
    timestamp: Date,
    atrIndicator: IndicatorValue
  ): Signal {
    return {
      id: `${symbol}_${timeframe}_volatility_${timestamp.getTime()}`,
      symbol,
      type: 'alert',
      strength: 'moderate',
      priority: 'medium',
      score: 70,
      confidence: 85,
      timeframe,
      timestamp,
      
      title: `High Volatility Alert - ${symbol}`,
      description: `${symbol} is experiencing high volatility`,
      reasoning: [`ATR indicates ${atrIndicator.metadata?.volatility} volatility`],
      
      indicators: [{
        id: 'ATR',
        name: 'Average True Range',
        type: 'volatility',
        value: atrIndicator.value,
        weight: 1.0,
        status: 'neutral',
        confirmation: true
      }],
      
      status: 'active',
      tags: ['volatility', 'alert'],
      category: 'risk_management'
    }
  }
  
  private createBreakoutAlert(
    symbol: string,
    timeframe: TimeFrame,
    currentPrice: number,
    timestamp: Date,
    srIndicator: IndicatorValue
  ): Signal {
    return {
      id: `${symbol}_${timeframe}_breakout_${timestamp.getTime()}`,
      symbol,
      type: 'alert',
      strength: 'strong',
      priority: 'high',
      score: 85,
      confidence: 90,
      timeframe,
      timestamp,
      
      title: `Breakout Alert - ${symbol}`,
      description: `${symbol} has broken key support/resistance level`,
      reasoning: [`Price broke ${srIndicator.metadata?.breakout} level`],
      
      indicators: [{
        id: 'SupportResistance',
        name: 'Support/Resistance',
        type: 'support_resistance',
        value: srIndicator.value,
        weight: 1.0,
        status: srIndicator.status,
        confirmation: true
      }],
      
      status: 'active',
      tags: ['breakout', 'alert', 'support_resistance'],
      category: 'price_action'
    }
  }
}
