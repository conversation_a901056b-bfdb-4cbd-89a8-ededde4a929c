/**
 * Rate Limiter for API calls
 * Implements token bucket algorithm with multiple providers
 */

interface RateLimitConfig {
  maxTokens: number
  refillRate: number // tokens per minute
  refillInterval: number // milliseconds
}

interface ApiProvider {
  name: string
  config: RateLimitConfig
  tokens: number
  lastRefill: number
  queue: Array<{
    resolve: (value: any) => void
    reject: (error: any) => void
    priority: number
  }>
}

class RateLimiter {
  private providers: Map<string, ApiProvider> = new Map()

  constructor() {
    // CoinGecko Free Tier: 10 calls/min (conservative)
    this.addProvider('coingecko', {
      maxTokens: 10,
      refillRate: 10,
      refillInterval: 60000 // 1 minute
    })

    // CoinMarketCap Free Tier: 30 calls/min
    this.addProvider('coinmarketcap', {
      maxTokens: 30,
      refillRate: 30,
      refillInterval: 60000 // 1 minute
    })

    // Start refill intervals
    this.startRefillIntervals()
  }

  private addProvider(name: string, config: RateLimitConfig) {
    this.providers.set(name, {
      name,
      config,
      tokens: config.maxTokens,
      lastRefill: Date.now(),
      queue: []
    })
  }

  private startRefillIntervals() {
    setInterval(() => {
      this.providers.forEach(provider => {
        this.refillTokens(provider)
        this.processQueue(provider)
      })
    }, 1000) // Check every second
  }

  private refillTokens(provider: ApiProvider) {
    const now = Date.now()
    const timePassed = now - provider.lastRefill
    
    if (timePassed >= provider.config.refillInterval) {
      provider.tokens = provider.config.maxTokens
      provider.lastRefill = now
    }
  }

  private processQueue(provider: ApiProvider) {
    while (provider.queue.length > 0 && provider.tokens > 0) {
      const request = provider.queue.shift()!
      provider.tokens--
      request.resolve(true)
    }
  }

  async waitForToken(providerName: string, priority: number = 0): Promise<void> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Unknown provider: ${providerName}`)
    }

    if (provider.tokens > 0) {
      provider.tokens--
      return Promise.resolve()
    }

    // Add to queue with priority
    return new Promise((resolve, reject) => {
      const request = { resolve, reject, priority }
      
      // Insert based on priority (higher priority first)
      const insertIndex = provider.queue.findIndex(r => r.priority < priority)
      if (insertIndex === -1) {
        provider.queue.push(request)
      } else {
        provider.queue.splice(insertIndex, 0, request)
      }

      // Timeout after 2 minutes
      setTimeout(() => {
        const index = provider.queue.indexOf(request)
        if (index > -1) {
          provider.queue.splice(index, 1)
          reject(new Error('Rate limit timeout'))
        }
      }, 120000)
    })
  }

  getStatus(providerName: string) {
    const provider = this.providers.get(providerName)
    if (!provider) return null

    return {
      name: provider.name,
      availableTokens: provider.tokens,
      maxTokens: provider.config.maxTokens,
      queueLength: provider.queue.length,
      nextRefill: provider.lastRefill + provider.config.refillInterval
    }
  }

  getAllStatus() {
    const status: Record<string, any> = {}
    this.providers.forEach((provider, name) => {
      status[name] = this.getStatus(name)
    })
    return status
  }
}

// Singleton instance
export const rateLimiter = new RateLimiter()

// Decorator for API calls
export function withRateLimit(provider: string, priority: number = 0) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      await rateLimiter.waitForToken(provider, priority)
      return method.apply(this, args)
    }

    return descriptor
  }
}

// Cache implementation
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

class ApiCache {
  private cache: Map<string, CacheEntry<any>> = new Map()

  set<T>(key: string, data: T, ttlMinutes: number = 5) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000
    })
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    if (!entry) return null

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return null
    }

    return entry.data as T
  }

  clear() {
    this.cache.clear()
  }

  size() {
    return this.cache.size
  }

  // Clean expired entries
  cleanup() {
    const now = Date.now()
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

export const apiCache = new ApiCache()

// Start cleanup interval
setInterval(() => {
  apiCache.cleanup()
}, 5 * 60 * 1000) // Every 5 minutes
