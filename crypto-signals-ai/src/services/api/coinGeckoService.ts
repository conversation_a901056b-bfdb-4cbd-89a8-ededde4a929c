import { rateLimiter, apiCache } from './rateLimiter'
import { config, hasApi<PERSON><PERSON>, getApi<PERSON><PERSON>, getApiBaseUrl } from '@/lib/config'

interface CoinGeckoPrice {
  id: string
  symbol: string
  name: string
  current_price: number
  market_cap: number
  market_cap_rank: number
  fully_diluted_valuation: number
  total_volume: number
  high_24h: number
  low_24h: number
  price_change_24h: number
  price_change_percentage_24h: number
  market_cap_change_24h: number
  market_cap_change_percentage_24h: number
  circulating_supply: number
  total_supply: number
  max_supply: number
  ath: number
  ath_change_percentage: number
  ath_date: string
  atl: number
  atl_change_percentage: number
  atl_date: string
  last_updated: string
}

interface CoinGeckoGlobalData {
  data: {
    active_cryptocurrencies: number
    upcoming_icos: number
    ongoing_icos: number
    ended_icos: number
    markets: number
    total_market_cap: Record<string, number>
    total_volume: Record<string, number>
    market_cap_percentage: Record<string, number>
    market_cap_change_percentage_24h_usd: number
    updated_at: number
  }
}

interface CoinGeckoHistoricalData {
  prices: [number, number][]
  market_caps: [number, number][]
  total_volumes: [number, number][]
}

class CoinGeckoService {
  private baseUrl = getApiBaseUrl('coinGecko')
  private apiKey: string | null = null

  constructor() {
    this.apiKey = getApiKey('coinGecko')
  }

  // Check if service is properly configured
  isConfigured(): boolean {
    return hasApiKey('coinGecko')
  }

  private async makeRequest<T>(endpoint: string, priority: number = 0): Promise<T> {
    // Check if API is configured
    if (!this.isConfigured()) {
      throw new Error('CoinGecko API key not configured. Please set NEXT_PUBLIC_COINGECKO_API_KEY')
    }

    await rateLimiter.waitForToken('coingecko', priority)

    const url = `${this.baseUrl}${endpoint}`
    const headers: Record<string, string> = {
      'Accept': 'application/json',
    }

    if (this.apiKey) {
      headers['x-cg-demo-api-key'] = this.apiKey
    }

    try {
      const response = await fetch(url, { headers })

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('CoinGecko rate limit exceeded. Please wait before making more requests.')
        }
        if (response.status === 401) {
          throw new Error('CoinGecko API key is invalid. Please check your configuration.')
        }
        throw new Error(`CoinGecko API error: ${response.status} ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('CoinGecko API request failed:', error)
      throw error
    }
  }

  async getMarketData(
    vs_currency: string = 'usd',
    order: string = 'market_cap_desc',
    per_page: number = 100,
    page: number = 1
  ): Promise<CoinGeckoPrice[]> {
    const cacheKey = `coingecko_market_${vs_currency}_${order}_${per_page}_${page}`
    const cached = apiCache.get<CoinGeckoPrice[]>(cacheKey)
    if (cached) return cached

    const endpoint = `/coins/markets?vs_currency=${vs_currency}&order=${order}&per_page=${per_page}&page=${page}&sparkline=false&price_change_percentage=24h`
    
    const data = await this.makeRequest<CoinGeckoPrice[]>(endpoint, 1)
    apiCache.set(cacheKey, data, 2) // Cache for 2 minutes
    return data
  }

  async getSimplePrice(
    ids: string[],
    vs_currencies: string[] = ['usd'],
    include_market_cap: boolean = true,
    include_24hr_vol: boolean = true,
    include_24hr_change: boolean = true
  ): Promise<Record<string, any>> {
    const cacheKey = `coingecko_simple_${ids.join(',')}_${vs_currencies.join(',')}`
    const cached = apiCache.get<Record<string, any>>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      ids: ids.join(','),
      vs_currencies: vs_currencies.join(','),
      include_market_cap: include_market_cap.toString(),
      include_24hr_vol: include_24hr_vol.toString(),
      include_24hr_change: include_24hr_change.toString(),
    })

    const endpoint = `/simple/price?${params.toString()}`
    const data = await this.makeRequest<Record<string, any>>(endpoint, 2)
    apiCache.set(cacheKey, data, 1) // Cache for 1 minute
    return data
  }

  async getGlobalData(): Promise<CoinGeckoGlobalData> {
    const cacheKey = 'coingecko_global'
    const cached = apiCache.get<CoinGeckoGlobalData>(cacheKey)
    if (cached) return cached

    const endpoint = '/global'
    const data = await this.makeRequest<CoinGeckoGlobalData>(endpoint, 1)
    apiCache.set(cacheKey, data, 5) // Cache for 5 minutes
    return data
  }

  async getCoinData(id: string): Promise<any> {
    const cacheKey = `coingecko_coin_${id}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const endpoint = `/coins/${id}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false`
    const data = await this.makeRequest<any>(endpoint, 1)
    apiCache.set(cacheKey, data, 10) // Cache for 10 minutes
    return data
  }

  async getHistoricalData(
    id: string,
    vs_currency: string = 'usd',
    days: number = 7
  ): Promise<CoinGeckoHistoricalData> {
    const cacheKey = `coingecko_historical_${id}_${vs_currency}_${days}`
    const cached = apiCache.get<CoinGeckoHistoricalData>(cacheKey)
    if (cached) return cached

    const endpoint = `/coins/${id}/market_chart?vs_currency=${vs_currency}&days=${days}`
    const data = await this.makeRequest<CoinGeckoHistoricalData>(endpoint, 0)
    apiCache.set(cacheKey, data, 30) // Cache for 30 minutes
    return data
  }

  async getTrendingCoins(): Promise<any> {
    const cacheKey = 'coingecko_trending'
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const endpoint = '/search/trending'
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 15) // Cache for 15 minutes
    return data
  }

  async getFearGreedIndex(): Promise<number> {
    // CoinGecko doesn't have Fear & Greed Index, we'll use alternative API
    const cacheKey = 'fear_greed_index'
    const cached = apiCache.get<number>(cacheKey)
    if (cached) return cached

    try {
      const response = await fetch('https://api.alternative.me/fng/')
      const data = await response.json()
      const index = parseInt(data.data[0].value)
      apiCache.set(cacheKey, index, 60) // Cache for 1 hour
      return index
    } catch (error) {
      console.error('Failed to fetch Fear & Greed Index:', error)
      return 50 // Default neutral value
    }
  }

  // Get exchange rates for BTC dominance calculation
  async getExchangeRates(): Promise<any> {
    const cacheKey = 'coingecko_exchange_rates'
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const endpoint = '/exchange_rates'
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 30) // Cache for 30 minutes
    return data
  }

  // Get supported coins list
  async getCoinsList(): Promise<Array<{id: string, symbol: string, name: string}>> {
    const cacheKey = 'coingecko_coins_list'
    const cached = apiCache.get<Array<{id: string, symbol: string, name: string}>>(cacheKey)
    if (cached) return cached

    const endpoint = '/coins/list'
    const data = await this.makeRequest<Array<{id: string, symbol: string, name: string}>>(endpoint, 0)
    apiCache.set(cacheKey, data, 1440) // Cache for 24 hours
    return data
  }

  // Get ping status
  async ping(): Promise<boolean> {
    try {
      await this.makeRequest<any>('/ping', 2)
      return true
    } catch (error) {
      return false
    }
  }

  // Get rate limit status
  getRateLimitStatus() {
    return rateLimiter.getStatus('coingecko')
  }
}

export const coinGeckoService = new CoinGeckoService()
