import { coinGeckoService } from './coinGeckoService'
import { coinMarketCapService } from './coinMarketCapService'
import { apiCache } from './rateLimiter'
import { CryptoAsset, MarketOverview, MarketData } from '@/types/market'

/**
 * Market Data Service - Aggregates data from multiple APIs
 * Strategy: Use CoinGecko as primary, CoinMarketCap as fallback/supplement
 */

class MarketDataService {
  private preferredProvider: 'coingecko' | 'coinmarketcap' = 'coingecko'

  // Symbol mapping between providers
  private symbolMap: Record<string, { cg: string; cmc: string }> = {
    'BTC': { cg: 'bitcoin', cmc: 'BTC' },
    'ETH': { cg: 'ethereum', cmc: 'ETH' },
    'BNB': { cg: 'binancecoin', cmc: 'BNB' },
    'SOL': { cg: 'solana', cmc: 'SOL' },
    'XRP': { cg: 'ripple', cmc: 'XRP' },
    'ADA': { cg: 'cardano', cmc: 'ADA' },
    'AVAX': { cg: 'avalanche-2', cmc: 'AVAX' },
    'DOT': { cg: 'polkadot', cmc: 'DOT' },
    'MATIC': { cg: 'matic-network', cmc: 'MATIC' },
    'LINK': { cg: 'chainlink', cmc: 'LINK' },
    'UNI': { cg: 'uniswap', cmc: 'UNI' },
    'LTC': { cg: 'litecoin', cmc: 'LTC' },
    'ATOM': { cg: 'cosmos', cmc: 'ATOM' },
    'FTM': { cg: 'fantom', cmc: 'FTM' },
    'ALGO': { cg: 'algorand', cmc: 'ALGO' },
    'VET': { cg: 'vechain', cmc: 'VET' }
  }

  async getTopCryptoAssets(limit: number = 100): Promise<CryptoAsset[]> {
    const cacheKey = `market_top_assets_${limit}`
    const cached = apiCache.get<CryptoAsset[]>(cacheKey)
    if (cached) return cached

    try {
      let assets: CryptoAsset[] = []

      if (this.preferredProvider === 'coingecko') {
        try {
          const data = await coinGeckoService.getMarketData('usd', 'market_cap_desc', limit)
          assets = data.map(coin => ({
            symbol: coin.symbol.toUpperCase(),
            name: coin.name,
            price: coin.current_price || 0,
            change24h: coin.price_change_percentage_24h || 0,
            volume24h: coin.total_volume || 0,
            marketCap: coin.market_cap || 0,
            rank: coin.market_cap_rank || 0,
            lastUpdated: new Date(coin.last_updated || Date.now())
          }))
        } catch (error) {
          console.warn('CoinGecko failed, trying CoinMarketCap:', error)
          // Fallback to CoinMarketCap
          const cmcData = await coinMarketCapService.getListings(1, limit)
          assets = cmcData.data.map(coin => ({
            symbol: coin.symbol,
            name: coin.name,
            price: coin.quote?.USD?.price || 0,
            change24h: coin.quote?.USD?.percent_change_24h || 0,
            volume24h: coin.quote?.USD?.volume_24h || 0,
            marketCap: coin.quote?.USD?.market_cap || 0,
            rank: coin.cmc_rank || 0,
            lastUpdated: new Date(coin.quote?.USD?.last_updated || Date.now())
          }))
        }
      } else {
        // Use CoinMarketCap as primary
        try {
          const cmcData = await coinMarketCapService.getListings(1, limit)
          assets = cmcData.data.map(coin => ({
            symbol: coin.symbol,
            name: coin.name,
            price: coin.quote?.USD?.price || 0,
            change24h: coin.quote?.USD?.percent_change_24h || 0,
            volume24h: coin.quote?.USD?.volume_24h || 0,
            marketCap: coin.quote?.USD?.market_cap || 0,
            rank: coin.cmc_rank || 0,
            lastUpdated: new Date(coin.quote?.USD?.last_updated || Date.now())
          }))
        } catch (error) {
          console.warn('CoinMarketCap failed, trying CoinGecko:', error)
          // Fallback to CoinGecko
          const data = await coinGeckoService.getMarketData('usd', 'market_cap_desc', limit)
          assets = data.map(coin => ({
            symbol: coin.symbol.toUpperCase(),
            name: coin.name,
            price: coin.current_price || 0,
            change24h: coin.price_change_percentage_24h || 0,
            volume24h: coin.total_volume || 0,
            marketCap: coin.market_cap || 0,
            rank: coin.market_cap_rank || 0,
            lastUpdated: new Date(coin.last_updated || Date.now())
          }))
        }
      }

      // Validate that we have some data
      if (assets.length === 0) {
        throw new Error('No market data available from any provider')
      }

      apiCache.set(cacheKey, assets, 2) // Cache for 2 minutes
      return assets
    } catch (error) {
      console.error('Failed to fetch crypto assets from both providers:', error)

      // Check if it's an API configuration issue
      if (error instanceof Error && error.message.includes('API key')) {
        throw new Error('API_NOT_CONFIGURED')
      }

      throw new Error('Unable to fetch market data. Please check your internet connection and API configuration.')
    }
  }

  async getMarketOverview(): Promise<MarketOverview> {
    const cacheKey = 'market_overview'
    const cached = apiCache.get<MarketOverview>(cacheKey)
    if (cached) return cached

    try {
      let overview: MarketOverview

      if (this.preferredProvider === 'coingecko') {
        try {
          const [globalData, fearGreedIndex] = await Promise.all([
            coinGeckoService.getGlobalData(),
            coinGeckoService.getFearGreedIndex()
          ])

          overview = {
            totalMarketCap: globalData.data.total_market_cap.usd,
            totalVolume24h: globalData.data.total_volume.usd,
            btcDominance: globalData.data.market_cap_percentage.btc,
            fearGreedIndex,
            activeCoins: globalData.data.active_cryptocurrencies,
            marketCapChange24h: globalData.data.market_cap_change_percentage_24h_usd,
            lastUpdated: new Date(globalData.data.updated_at * 1000)
          }
        } catch (error) {
          console.warn('CoinGecko global data failed, trying CoinMarketCap:', error)
          // Fallback to CoinMarketCap
          const cmcGlobal = await coinMarketCapService.getGlobalMetrics()
          const fearGreedIndex = await coinGeckoService.getFearGreedIndex() // Still try to get from alternative API

          overview = {
            totalMarketCap: cmcGlobal.data.quote.USD.total_market_cap,
            totalVolume24h: cmcGlobal.data.quote.USD.total_volume_24h,
            btcDominance: cmcGlobal.data.btc_dominance,
            fearGreedIndex,
            activeCoins: cmcGlobal.data.active_cryptocurrencies,
            marketCapChange24h: cmcGlobal.data.quote.USD.total_market_cap_yesterday_percentage_change,
            lastUpdated: new Date(cmcGlobal.data.quote.USD.last_updated)
          }
        }
      } else {
        // Use CoinMarketCap as primary
        try {
          const [cmcGlobal, fearGreedIndex] = await Promise.all([
            coinMarketCapService.getGlobalMetrics(),
            coinGeckoService.getFearGreedIndex()
          ])

          overview = {
            totalMarketCap: cmcGlobal.data.quote.USD.total_market_cap,
            totalVolume24h: cmcGlobal.data.quote.USD.total_volume_24h,
            btcDominance: cmcGlobal.data.btc_dominance,
            fearGreedIndex,
            activeCoins: cmcGlobal.data.active_cryptocurrencies,
            marketCapChange24h: cmcGlobal.data.quote.USD.total_market_cap_yesterday_percentage_change,
            lastUpdated: new Date(cmcGlobal.data.quote.USD.last_updated)
          }
        } catch (error) {
          console.warn('CoinMarketCap global data failed, trying CoinGecko:', error)
          // Fallback to CoinGecko
          const [globalData, fearGreedIndex] = await Promise.all([
            coinGeckoService.getGlobalData(),
            coinGeckoService.getFearGreedIndex()
          ])

          overview = {
            totalMarketCap: globalData.data.total_market_cap.usd,
            totalVolume24h: globalData.data.total_volume.usd,
            btcDominance: globalData.data.market_cap_percentage.btc,
            fearGreedIndex,
            activeCoins: globalData.data.active_cryptocurrencies,
            marketCapChange24h: globalData.data.market_cap_change_percentage_24h_usd,
            lastUpdated: new Date(globalData.data.updated_at * 1000)
          }
        }
      }

      apiCache.set(cacheKey, overview, 5) // Cache for 5 minutes
      return overview
    } catch (error) {
      console.error('Failed to fetch market overview from both providers:', error)
      throw new Error('Unable to fetch market overview')
    }
  }

  async getHistoricalData(symbol: string, days: number = 7): Promise<MarketData[]> {
    const cacheKey = `market_historical_${symbol}_${days}`
    const cached = apiCache.get<MarketData[]>(cacheKey)
    if (cached) return cached

    try {
      const coinId = this.symbolMap[symbol]?.cg || symbol.toLowerCase()
      const data = await coinGeckoService.getHistoricalData(coinId, 'usd', days)
      
      const marketData: MarketData[] = data.prices.map((price, index) => ({
        timestamp: new Date(price[0]),
        open: price[1], // CoinGecko doesn't provide OHLC, using price as approximation
        high: price[1] * 1.01, // Approximate high
        low: price[1] * 0.99, // Approximate low
        close: price[1],
        volume: data.total_volumes[index] ? data.total_volumes[index][1] : 0
      }))

      apiCache.set(cacheKey, marketData, 30) // Cache for 30 minutes
      return marketData
    } catch (error) {
      console.error(`Failed to fetch historical data for ${symbol}:`, error)
      throw new Error(`Unable to fetch historical data for ${symbol}`)
    }
  }

  async getSpecificCoinData(symbols: string[]): Promise<Record<string, any>> {
    const cacheKey = `market_specific_${symbols.join(',')}`
    const cached = apiCache.get<Record<string, any>>(cacheKey)
    if (cached) return cached

    try {
      let data: Record<string, any> = {}

      if (this.preferredProvider === 'coingecko') {
        try {
          const coinIds = symbols.map(symbol => this.symbolMap[symbol]?.cg || symbol.toLowerCase())
          data = await coinGeckoService.getSimplePrice(coinIds, ['usd'], true, true, true)
        } catch (error) {
          console.warn('CoinGecko specific data failed, trying CoinMarketCap:', error)
          data = await coinMarketCapService.getQuotes(symbols)
        }
      } else {
        try {
          data = await coinMarketCapService.getQuotes(symbols)
        } catch (error) {
          console.warn('CoinMarketCap specific data failed, trying CoinGecko:', error)
          const coinIds = symbols.map(symbol => this.symbolMap[symbol]?.cg || symbol.toLowerCase())
          data = await coinGeckoService.getSimplePrice(coinIds, ['usd'], true, true, true)
        }
      }

      apiCache.set(cacheKey, data, 1) // Cache for 1 minute
      return data
    } catch (error) {
      console.error('Failed to fetch specific coin data from both providers:', error)
      throw new Error('Unable to fetch specific coin data')
    }
  }

  // Switch preferred provider
  setPreferredProvider(provider: 'coingecko' | 'coinmarketcap') {
    this.preferredProvider = provider
  }

  // Get API status
  async getApiStatus() {
    const [cgStatus, cmcStatus] = await Promise.all([
      coinGeckoService.ping(),
      coinMarketCapService.ping()
    ])

    return {
      coingecko: {
        available: cgStatus,
        rateLimitStatus: coinGeckoService.getRateLimitStatus()
      },
      coinmarketcap: {
        available: cmcStatus,
        rateLimitStatus: coinMarketCapService.getRateLimitStatus()
      },
      preferredProvider: this.preferredProvider
    }
  }

  // Get cache statistics
  getCacheStats() {
    return {
      size: apiCache.size(),
      // Add more cache statistics as needed
    }
  }
}

export const marketDataService = new MarketDataService()
