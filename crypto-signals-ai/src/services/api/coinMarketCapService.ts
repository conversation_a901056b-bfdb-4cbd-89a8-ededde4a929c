import { rateLimiter, apiCache } from './rateLimiter'

interface CMCQuote {
  price: number
  volume_24h: number
  volume_change_24h: number
  percent_change_1h: number
  percent_change_24h: number
  percent_change_7d: number
  percent_change_30d: number
  percent_change_60d: number
  percent_change_90d: number
  market_cap: number
  market_cap_dominance: number
  fully_diluted_market_cap: number
  last_updated: string
}

interface CMCCryptocurrency {
  id: number
  name: string
  symbol: string
  slug: string
  num_market_pairs: number
  date_added: string
  tags: string[]
  max_supply: number
  circulating_supply: number
  total_supply: number
  platform: any
  cmc_rank: number
  self_reported_circulating_supply: number
  self_reported_market_cap: number
  tvl_ratio: number
  last_updated: string
  quote: Record<string, CMCQuote>
}

interface CMCListingsResponse {
  status: {
    timestamp: string
    error_code: number
    error_message: string
    elapsed: number
    credit_count: number
    notice: string
  }
  data: CMCCryptocurrency[]
}

interface CMCGlobalMetrics {
  status: {
    timestamp: string
    error_code: number
    error_message: string
    elapsed: number
    credit_count: number
  }
  data: {
    active_cryptocurrencies: number
    total_cryptocurrencies: number
    active_market_pairs: number
    active_exchanges: number
    total_exchanges: number
    eth_dominance: number
    btc_dominance: number
    eth_dominance_yesterday: number
    btc_dominance_yesterday: number
    eth_dominance_24h_percentage_change: number
    btc_dominance_24h_percentage_change: number
    defi_volume_24h: number
    defi_volume_24h_reported: number
    defi_24h_percentage_change: number
    stablecoin_volume_24h: number
    stablecoin_volume_24h_reported: number
    stablecoin_24h_percentage_change: number
    derivatives_volume_24h: number
    derivatives_volume_24h_reported: number
    derivatives_24h_percentage_change: number
    quote: Record<string, {
      total_market_cap: number
      total_volume_24h: number
      total_volume_24h_reported: number
      altcoin_volume_24h: number
      altcoin_volume_24h_reported: number
      altcoin_market_cap: number
      defi_volume_24h: number
      defi_volume_24h_reported: number
      defi_24h_percentage_change: number
      stablecoin_volume_24h: number
      stablecoin_volume_24h_reported: number
      stablecoin_24h_percentage_change: number
      derivatives_volume_24h: number
      derivatives_volume_24h_reported: number
      derivatives_24h_percentage_change: number
      total_market_cap_yesterday: number
      total_volume_24h_yesterday: number
      total_market_cap_yesterday_percentage_change: number
      total_volume_24h_yesterday_percentage_change: number
      last_updated: string
    }>
    last_updated: string
  }
}

class CoinMarketCapService {
  private baseUrl = 'https://pro-api.coinmarketcap.com/v1'
  private apiKey: string | null = null

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_COINMARKETCAP_API_KEY || null
  }

  private async makeRequest<T>(endpoint: string, priority: number = 0): Promise<T> {
    if (!this.apiKey) {
      throw new Error('CoinMarketCap API key is required')
    }

    await rateLimiter.waitForToken('coinmarketcap', priority)

    const url = `${this.baseUrl}${endpoint}`
    const headers = {
      'Accept': 'application/json',
      'X-CMC_PRO_API_KEY': this.apiKey,
    }

    try {
      const response = await fetch(url, { headers })

      if (!response.ok) {
        if (response.status === 429) {
          throw new Error('Rate limit exceeded')
        }
        throw new Error(`CoinMarketCap API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      // Check for API errors in response
      if (data.status && data.status.error_code !== 0) {
        throw new Error(`CoinMarketCap API error: ${data.status.error_message}`)
      }

      return data
    } catch (error) {
      console.error('CoinMarketCap API request failed:', error)
      throw error
    }
  }

  async getListings(
    start: number = 1,
    limit: number = 100,
    convert: string = 'USD',
    sort: string = 'market_cap'
  ): Promise<CMCListingsResponse> {
    const cacheKey = `cmc_listings_${start}_${limit}_${convert}_${sort}`
    const cached = apiCache.get<CMCListingsResponse>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      start: start.toString(),
      limit: limit.toString(),
      convert,
      sort,
    })

    const endpoint = `/cryptocurrency/listings/latest?${params.toString()}`
    const data = await this.makeRequest<CMCListingsResponse>(endpoint, 1)
    apiCache.set(cacheKey, data, 2) // Cache for 2 minutes
    return data
  }

  async getQuotes(
    symbols: string[],
    convert: string = 'USD'
  ): Promise<any> {
    const cacheKey = `cmc_quotes_${symbols.join(',')}_${convert}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      symbol: symbols.join(','),
      convert,
    })

    const endpoint = `/cryptocurrency/quotes/latest?${params.toString()}`
    const data = await this.makeRequest<any>(endpoint, 2)
    apiCache.set(cacheKey, data, 1) // Cache for 1 minute
    return data
  }

  async getGlobalMetrics(convert: string = 'USD'): Promise<CMCGlobalMetrics> {
    const cacheKey = `cmc_global_${convert}`
    const cached = apiCache.get<CMCGlobalMetrics>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({ convert })
    const endpoint = `/global-metrics/quotes/latest?${params.toString()}`
    const data = await this.makeRequest<CMCGlobalMetrics>(endpoint, 1)
    apiCache.set(cacheKey, data, 5) // Cache for 5 minutes
    return data
  }

  async getCryptocurrencyInfo(symbols: string[]): Promise<any> {
    const cacheKey = `cmc_info_${symbols.join(',')}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      symbol: symbols.join(','),
    })

    const endpoint = `/cryptocurrency/info?${params.toString()}`
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 60) // Cache for 1 hour
    return data
  }

  async getHistoricalQuotes(
    symbol: string,
    time_start?: string,
    time_end?: string,
    count?: number,
    interval?: string,
    convert: string = 'USD'
  ): Promise<any> {
    const cacheKey = `cmc_historical_${symbol}_${time_start}_${time_end}_${count}_${interval}_${convert}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      symbol,
      convert,
    })

    if (time_start) params.append('time_start', time_start)
    if (time_end) params.append('time_end', time_end)
    if (count) params.append('count', count.toString())
    if (interval) params.append('interval', interval)

    const endpoint = `/cryptocurrency/quotes/historical?${params.toString()}`
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 30) // Cache for 30 minutes
    return data
  }

  async getTrendingGainersLosers(
    start: number = 1,
    limit: number = 10,
    time_period: string = '24h',
    convert: string = 'USD'
  ): Promise<any> {
    const cacheKey = `cmc_trending_${start}_${limit}_${time_period}_${convert}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      start: start.toString(),
      limit: limit.toString(),
      time_period,
      convert,
    })

    const endpoint = `/cryptocurrency/trending/gainers-losers?${params.toString()}`
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 15) // Cache for 15 minutes
    return data
  }

  async getExchangeListings(
    start: number = 1,
    limit: number = 100,
    sort: string = 'volume_24h'
  ): Promise<any> {
    const cacheKey = `cmc_exchanges_${start}_${limit}_${sort}`
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const params = new URLSearchParams({
      start: start.toString(),
      limit: limit.toString(),
      sort,
    })

    const endpoint = `/exchange/listings/latest?${params.toString()}`
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 10) // Cache for 10 minutes
    return data
  }

  // Get cryptocurrency map (ID to symbol mapping)
  async getCryptocurrencyMap(): Promise<any> {
    const cacheKey = 'cmc_crypto_map'
    const cached = apiCache.get<any>(cacheKey)
    if (cached) return cached

    const endpoint = '/cryptocurrency/map'
    const data = await this.makeRequest<any>(endpoint, 0)
    apiCache.set(cacheKey, data, 1440) // Cache for 24 hours
    return data
  }

  // Get rate limit status
  getRateLimitStatus() {
    return rateLimiter.getStatus('coinmarketcap')
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      await this.getGlobalMetrics()
      return true
    } catch (error) {
      return false
    }
  }
}

export const coinMarketCapService = new CoinMarketCapService()
