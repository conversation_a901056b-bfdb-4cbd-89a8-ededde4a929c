'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

interface WebSocketMessage {
  type: 'price_update' | 'signal_alert' | 'market_update' | 'system_message'
  data: any
  timestamp: number
}

interface UseWebSocketReturn {
  isConnected: boolean
  lastMessage: WebSocketMessage | null
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  sendMessage: (message: any) => void
  reconnect: () => void
  disconnect: () => void
}

export function useWebSocket(url?: string): UseWebSocketReturn {
  const [isConnected, setIsConnected] = useState(false)
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const messageIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Mock WebSocket implementation for demo
  const simulateWebSocket = useCallback(() => {
    setConnectionStatus('connecting')
    
    // Simulate connection delay
    setTimeout(() => {
      setIsConnected(true)
      setConnectionStatus('connected')
      
      // Start sending mock messages
      messageIntervalRef.current = setInterval(() => {
        const messageTypes: WebSocketMessage['type'][] = ['price_update', 'signal_alert', 'market_update']
        const randomType = messageTypes[Math.floor(Math.random() * messageTypes.length)]
        
        let mockData: any
        
        switch (randomType) {
          case 'price_update':
            mockData = {
              symbol: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'][Math.floor(Math.random() * 3)],
              price: 50000 + Math.random() * 10000,
              change24h: (Math.random() - 0.5) * 10,
              volume: Math.random() * 1000000
            }
            break
            
          case 'signal_alert':
            mockData = {
              id: `signal_${Date.now()}`,
              symbol: ['BTCUSDT', 'ETHUSDT', 'SOLUSDT'][Math.floor(Math.random() * 3)],
              type: ['buy', 'sell', 'alert'][Math.floor(Math.random() * 3)],
              strength: ['weak', 'moderate', 'strong'][Math.floor(Math.random() * 3)],
              score: Math.floor(Math.random() * 40) + 60,
              message: 'New trading signal detected'
            }
            break
            
          case 'market_update':
            mockData = {
              totalMarketCap: 2340000000000 + (Math.random() - 0.5) * 100000000000,
              btcDominance: 52.4 + (Math.random() - 0.5) * 2,
              fearGreedIndex: 65 + (Math.random() - 0.5) * 10
            }
            break
            
          default:
            mockData = { message: 'System update' }
        }
        
        const message: WebSocketMessage = {
          type: randomType,
          data: mockData,
          timestamp: Date.now()
        }
        
        setLastMessage(message)
      }, 5000 + Math.random() * 10000) // Random interval between 5-15 seconds
      
    }, 1000)
  }, [])

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    // For demo purposes, we'll simulate WebSocket
    if (!url || url.includes('mock')) {
      simulateWebSocket()
      return
    }

    try {
      setConnectionStatus('connecting')
      wsRef.current = new WebSocket(url)

      wsRef.current.onopen = () => {
        setIsConnected(true)
        setConnectionStatus('connected')
        console.log('WebSocket connected')
      }

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data)
          setLastMessage(message)
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }

      wsRef.current.onclose = () => {
        setIsConnected(false)
        setConnectionStatus('disconnected')
        console.log('WebSocket disconnected')
        
        // Auto-reconnect after 5 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          connect()
        }, 5000)
      }

      wsRef.current.onerror = (error) => {
        setConnectionStatus('error')
        console.error('WebSocket error:', error)
      }
    } catch (error) {
      setConnectionStatus('error')
      console.error('Failed to create WebSocket connection:', error)
    }
  }, [url, simulateWebSocket])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    if (messageIntervalRef.current) {
      clearInterval(messageIntervalRef.current)
      messageIntervalRef.current = null
    }

    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    setIsConnected(false)
    setConnectionStatus('disconnected')
  }, [])

  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(connect, 1000)
  }, [disconnect, connect])

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }, [])

  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return {
    isConnected,
    lastMessage,
    connectionStatus,
    sendMessage,
    reconnect,
    disconnect
  }
}

// Hook for handling notifications
export function useNotifications() {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSupported, setIsSupported] = useState(false)

  useEffect(() => {
    setIsSupported('Notification' in window)
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  const requestPermission = useCallback(async () => {
    if (!isSupported) return false

    try {
      const result = await Notification.requestPermission()
      setPermission(result)
      return result === 'granted'
    } catch (error) {
      console.error('Failed to request notification permission:', error)
      return false
    }
  }, [isSupported])

  const showNotification = useCallback((title: string, options?: NotificationOptions) => {
    if (!isSupported || permission !== 'granted') {
      console.warn('Notifications not supported or permission not granted')
      return null
    }

    try {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options
      })

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close()
      }, 5000)

      return notification
    } catch (error) {
      console.error('Failed to show notification:', error)
      return null
    }
  }, [isSupported, permission])

  const showSignalNotification = useCallback((signal: any) => {
    const title = `${signal.type.toUpperCase()} Signal - ${signal.symbol}`
    const body = `${signal.strength} signal detected with ${signal.score}% confidence`
    
    return showNotification(title, {
      body,
      tag: `signal-${signal.id}`,
      requireInteraction: signal.priority === 'critical'
    })
  }, [showNotification])

  return {
    isSupported,
    permission,
    requestPermission,
    showNotification,
    showSignalNotification
  }
}
