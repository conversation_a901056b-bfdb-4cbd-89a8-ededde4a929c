'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Signal, SignalFilter, SignalType, SignalStrength, SignalPriority } from '@/types/signals'
import { TimeFrame } from '@/types/market'
import { marketDataService } from '@/services/api/marketDataService'
import { RealSignalGenerator } from '@/services/signals/realSignalGenerator'

interface UseSignalsReturn {
  signals: Signal[]
  filteredSignals: Signal[]
  isLoading: boolean
  error: string | null
  filter: SignalFilter
  updateFilter: (newFilter: Partial<SignalFilter>) => void
  refreshSignals: () => Promise<void>
  getSignalsByType: (type: SignalType) => Signal[]
  getSignalsByStrength: (strength: SignalStrength) => Signal[]
  getSignalsByPriority: (priority: SignalPriority) => Signal[]
  markSignalAsRead: (signalId: string) => void
  dismissSignal: (signalId: string) => void
}

// Real signal generator using market data and APIs
const generateRealSignals = async (): Promise<Signal[]> => {
  try {
    // Get real market data from APIs
    const cryptoAssets = await marketDataService.getTopCryptoAssets(15) // Top 15 coins
    const signalGenerator = new RealSignalGenerator()

    const signals: Signal[] = []

    // Generate signals for top cryptocurrencies using real data
    for (const asset of cryptoAssets.slice(0, 8)) { // Limit to top 8 to respect rate limits
      try {
        // Get real historical data for technical analysis
        const historicalData = await marketDataService.getHistoricalData(asset.symbol, 7)

        if (historicalData.length >= 20) { // Need sufficient data for analysis
          // Generate real signals based on technical analysis
          const assetSignals = await signalGenerator.generateSignals(
            `${asset.symbol}/USDT`,
            '1h',
            historicalData
          )

          signals.push(...assetSignals)
        }
      } catch (error) {
        console.warn(`Failed to generate signals for ${asset.symbol}:`, error)
      }

      // Add small delay to respect rate limits
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    return signals.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  } catch (error) {
    console.error('Failed to generate real signals:', error)
    return []
  }
}

const generateSignalDescription = (type: SignalType, strength: SignalStrength, symbol: string): string => {
  const descriptions = {
    buy: {
      weak: `Potential buying opportunity detected for ${symbol} with moderate confluence.`,
      moderate: `Multiple indicators suggest bullish momentum building for ${symbol}.`,
      strong: `Strong bullish confluence detected for ${symbol} with high probability setup.`
    },
    sell: {
      weak: `Potential selling pressure detected for ${symbol} with some bearish signals.`,
      moderate: `Multiple indicators suggest bearish momentum developing for ${symbol}.`,
      strong: `Strong bearish confluence detected for ${symbol} with high probability reversal.`
    },
    alert: {
      weak: `Market condition alert for ${symbol} - monitor for potential changes.`,
      moderate: `Important market development for ${symbol} requires attention.`,
      strong: `Critical market alert for ${symbol} - immediate attention recommended.`
    }
  }

  return descriptions[type][strength]
}

const generateSignalReasoning = (type: SignalType, strength: SignalStrength): string[] => {
  const baseReasons = {
    buy: [
      'RSI showing oversold recovery with bullish divergence',
      'MACD histogram turning positive with bullish crossover',
      'Price breaking above key resistance level',
      'Volume confirming the breakout with above-average activity',
      'Support level holding strong with multiple tests'
    ],
    sell: [
      'RSI showing overbought conditions with bearish divergence',
      'MACD histogram turning negative with bearish crossover',
      'Price failing at key resistance level',
      'Volume declining on recent rallies',
      'Resistance level acting as strong ceiling'
    ],
    alert: [
      'High volatility detected with ATR spike',
      'Unusual volume activity observed',
      'Key support/resistance level being tested',
      'Market structure change detected',
      'Funding rates reaching extreme levels'
    ]
  }

  const reasons = baseReasons[type]
  const count = strength === 'strong' ? 4 : strength === 'moderate' ? 3 : 2
  
  return reasons.slice(0, count).map(reason => 
    `${reason} (${Math.floor(Math.random() * 20) + 70}% strength)`
  )
}

const generateSignalIndicators = (type: SignalType, strength: SignalStrength) => {
  const indicators = [
    { id: 'RSI', name: 'RSI', type: 'momentum' as const, weight: 0.15 },
    { id: 'MACD', name: 'MACD', type: 'momentum' as const, weight: 0.20 },
    { id: 'Volume', name: 'Volume', type: 'volume' as const, weight: 0.10 },
    { id: 'BollingerBands', name: 'Bollinger Bands', type: 'volatility' as const, weight: 0.15 },
    { id: 'SupportResistance', name: 'Support/Resistance', type: 'support_resistance' as const, weight: 0.12 }
  ]

  const count = strength === 'strong' ? 5 : strength === 'moderate' ? 4 : 3
  const status = type === 'buy' ? 'bullish' : type === 'sell' ? 'bearish' : 'neutral'

  return indicators.slice(0, count).map(ind => ({
    ...ind,
    value: Math.random() * 100,
    status: status as 'bullish' | 'bearish' | 'neutral',
    confirmation: Math.random() > 0.2 // 80% confirmation rate
  }))
}

const generateSignalTags = (type: SignalType, strength: SignalStrength): string[] => {
  const baseTags = [type, 'technical']
  
  if (strength === 'strong') baseTags.push('high-confidence')
  if (Math.random() > 0.5) baseTags.push('momentum')
  if (Math.random() > 0.7) baseTags.push('breakout')
  
  return baseTags
}

export function useSignals(): UseSignalsReturn {
  const [signals, setSignals] = useState<Signal[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<SignalFilter>({})

  const fetchSignals = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const newSignals = await generateRealSignals()
      setSignals(newSignals)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch signals')
      console.error('Signal fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const refreshSignals = useCallback(async () => {
    await fetchSignals()
  }, [fetchSignals])

  const updateFilter = useCallback((newFilter: Partial<SignalFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }))
  }, [])

  const filteredSignals = useMemo(() => {
    return signals.filter(signal => {
      if (filter.symbols && !filter.symbols.includes(signal.symbol)) return false
      if (filter.types && !filter.types.includes(signal.type)) return false
      if (filter.strengths && !filter.strengths.includes(signal.strength)) return false
      if (filter.priorities && !filter.priorities.includes(signal.priority)) return false
      if (filter.timeframes && !filter.timeframes.includes(signal.timeframe)) return false
      if (filter.minScore && signal.score < filter.minScore) return false
      if (filter.maxAge) {
        const ageHours = (Date.now() - signal.timestamp.getTime()) / (1000 * 60 * 60)
        if (ageHours > filter.maxAge) return false
      }
      if (filter.categories && !filter.categories.includes(signal.category)) return false
      if (filter.tags && !filter.tags.some(tag => signal.tags.includes(tag))) return false

      return true
    })
  }, [signals, filter])

  const getSignalsByType = useCallback((type: SignalType) => {
    return signals.filter(signal => signal.type === type)
  }, [signals])

  const getSignalsByStrength = useCallback((strength: SignalStrength) => {
    return signals.filter(signal => signal.strength === strength)
  }, [signals])

  const getSignalsByPriority = useCallback((priority: SignalPriority) => {
    return signals.filter(signal => signal.priority === priority)
  }, [signals])

  const markSignalAsRead = useCallback((signalId: string) => {
    setSignals(prev => prev.map(signal => 
      signal.id === signalId 
        ? { ...signal, status: 'triggered' as const }
        : signal
    ))
  }, [])

  const dismissSignal = useCallback((signalId: string) => {
    setSignals(prev => prev.filter(signal => signal.id !== signalId))
  }, [])

  useEffect(() => {
    fetchSignals()
  }, [fetchSignals])

  // Auto-refresh signals every 5 minutes (to respect rate limits)
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchSignals()
      }
    }, 300000) // 5 minutes

    return () => clearInterval(interval)
  }, [fetchSignals, isLoading])

  return {
    signals,
    filteredSignals,
    isLoading,
    error,
    filter,
    updateFilter,
    refreshSignals,
    getSignalsByType,
    getSignalsByStrength,
    getSignalsByPriority,
    markSignalAsRead,
    dismissSignal
  }
}
