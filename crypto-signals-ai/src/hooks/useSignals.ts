'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Signal, SignalFilter, SignalType, SignalStrength, SignalPriority } from '@/types/signals'
import { TimeFrame } from '@/types/market'

interface UseSignalsReturn {
  signals: Signal[]
  filteredSignals: Signal[]
  isLoading: boolean
  error: string | null
  filter: SignalFilter
  updateFilter: (newFilter: Partial<SignalFilter>) => void
  refreshSignals: () => Promise<void>
  getSignalsByType: (type: SignalType) => Signal[]
  getSignalsByStrength: (strength: SignalStrength) => Signal[]
  getSignalsByPriority: (priority: SignalPriority) => Signal[]
  markSignalAsRead: (signalId: string) => void
  dismissSignal: (signalId: string) => void
}

// Mock signal generator
const generateMockSignals = (): Signal[] => {
  const symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'ADA/USDT', 'AVAX/USDT', 'DOT/USDT']
  const timeframes: TimeFrame[] = ['1m', '5m', '15m', '1h', '4h', '1d']
  const types: SignalType[] = ['buy', 'sell', 'alert']
  const strengths: SignalStrength[] = ['weak', 'moderate', 'strong']
  const priorities: SignalPriority[] = ['low', 'medium', 'high', 'critical']

  const signals: Signal[] = []

  for (let i = 0; i < 15; i++) {
    const symbol = symbols[Math.floor(Math.random() * symbols.length)]
    const type = types[Math.floor(Math.random() * types.length)]
    const strength = strengths[Math.floor(Math.random() * strengths.length)]
    const priority = priorities[Math.floor(Math.random() * priorities.length)]
    const timeframe = timeframes[Math.floor(Math.random() * timeframes.length)]
    
    const score = Math.floor(Math.random() * 40) + 60 // 60-100
    const confidence = Math.floor(Math.random() * 30) + 70 // 70-100
    const basePrice = symbol.includes('BTC') ? 52000 : symbol.includes('ETH') ? 3200 : 100
    
    const entryPrice = basePrice * (1 + (Math.random() - 0.5) * 0.02)
    const targetPrice = type === 'buy' 
      ? entryPrice * (1 + Math.random() * 0.05 + 0.02)
      : entryPrice * (1 - Math.random() * 0.05 - 0.02)
    const stopLoss = type === 'buy'
      ? entryPrice * (1 - Math.random() * 0.02 - 0.01)
      : entryPrice * (1 + Math.random() * 0.02 + 0.01)

    const signal: Signal = {
      id: `signal_${i}_${Date.now()}`,
      symbol,
      type,
      strength,
      priority,
      score,
      confidence,
      timeframe,
      timestamp: new Date(Date.now() - Math.random() * 3600000), // Last hour
      expiresAt: new Date(Date.now() + Math.random() * 7200000), // Next 2 hours
      
      entryPrice: type !== 'alert' ? entryPrice : undefined,
      targetPrice: type !== 'alert' ? targetPrice : undefined,
      stopLoss: type !== 'alert' ? stopLoss : undefined,
      
      title: `${strength.charAt(0).toUpperCase() + strength.slice(1)} ${type.charAt(0).toUpperCase() + type.slice(1)} Signal - ${symbol}`,
      description: generateSignalDescription(type, strength, symbol),
      reasoning: generateSignalReasoning(type, strength),
      
      indicators: generateSignalIndicators(type, strength),
      
      riskReward: type !== 'alert' ? Math.random() * 2 + 1 : undefined,
      
      status: 'active',
      tags: generateSignalTags(type, strength),
      category: 'technical_analysis'
    }

    signals.push(signal)
  }

  return signals.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}

const generateSignalDescription = (type: SignalType, strength: SignalStrength, symbol: string): string => {
  const descriptions = {
    buy: {
      weak: `Potential buying opportunity detected for ${symbol} with moderate confluence.`,
      moderate: `Multiple indicators suggest bullish momentum building for ${symbol}.`,
      strong: `Strong bullish confluence detected for ${symbol} with high probability setup.`
    },
    sell: {
      weak: `Potential selling pressure detected for ${symbol} with some bearish signals.`,
      moderate: `Multiple indicators suggest bearish momentum developing for ${symbol}.`,
      strong: `Strong bearish confluence detected for ${symbol} with high probability reversal.`
    },
    alert: {
      weak: `Market condition alert for ${symbol} - monitor for potential changes.`,
      moderate: `Important market development for ${symbol} requires attention.`,
      strong: `Critical market alert for ${symbol} - immediate attention recommended.`
    }
  }

  return descriptions[type][strength]
}

const generateSignalReasoning = (type: SignalType, strength: SignalStrength): string[] => {
  const baseReasons = {
    buy: [
      'RSI showing oversold recovery with bullish divergence',
      'MACD histogram turning positive with bullish crossover',
      'Price breaking above key resistance level',
      'Volume confirming the breakout with above-average activity',
      'Support level holding strong with multiple tests'
    ],
    sell: [
      'RSI showing overbought conditions with bearish divergence',
      'MACD histogram turning negative with bearish crossover',
      'Price failing at key resistance level',
      'Volume declining on recent rallies',
      'Resistance level acting as strong ceiling'
    ],
    alert: [
      'High volatility detected with ATR spike',
      'Unusual volume activity observed',
      'Key support/resistance level being tested',
      'Market structure change detected',
      'Funding rates reaching extreme levels'
    ]
  }

  const reasons = baseReasons[type]
  const count = strength === 'strong' ? 4 : strength === 'moderate' ? 3 : 2
  
  return reasons.slice(0, count).map(reason => 
    `${reason} (${Math.floor(Math.random() * 20) + 70}% strength)`
  )
}

const generateSignalIndicators = (type: SignalType, strength: SignalStrength) => {
  const indicators = [
    { id: 'RSI', name: 'RSI', type: 'momentum' as const, weight: 0.15 },
    { id: 'MACD', name: 'MACD', type: 'momentum' as const, weight: 0.20 },
    { id: 'Volume', name: 'Volume', type: 'volume' as const, weight: 0.10 },
    { id: 'BollingerBands', name: 'Bollinger Bands', type: 'volatility' as const, weight: 0.15 },
    { id: 'SupportResistance', name: 'Support/Resistance', type: 'support_resistance' as const, weight: 0.12 }
  ]

  const count = strength === 'strong' ? 5 : strength === 'moderate' ? 4 : 3
  const status = type === 'buy' ? 'bullish' : type === 'sell' ? 'bearish' : 'neutral'

  return indicators.slice(0, count).map(ind => ({
    ...ind,
    value: Math.random() * 100,
    status: status as 'bullish' | 'bearish' | 'neutral',
    confirmation: Math.random() > 0.2 // 80% confirmation rate
  }))
}

const generateSignalTags = (type: SignalType, strength: SignalStrength): string[] => {
  const baseTags = [type, 'technical']
  
  if (strength === 'strong') baseTags.push('high-confidence')
  if (Math.random() > 0.5) baseTags.push('momentum')
  if (Math.random() > 0.7) baseTags.push('breakout')
  
  return baseTags
}

export function useSignals(): UseSignalsReturn {
  const [signals, setSignals] = useState<Signal[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState<SignalFilter>({})

  const fetchSignals = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300))

      const newSignals = generateMockSignals()
      setSignals(newSignals)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch signals')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const refreshSignals = useCallback(async () => {
    await fetchSignals()
  }, [fetchSignals])

  const updateFilter = useCallback((newFilter: Partial<SignalFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }))
  }, [])

  const filteredSignals = useMemo(() => {
    return signals.filter(signal => {
      if (filter.symbols && !filter.symbols.includes(signal.symbol)) return false
      if (filter.types && !filter.types.includes(signal.type)) return false
      if (filter.strengths && !filter.strengths.includes(signal.strength)) return false
      if (filter.priorities && !filter.priorities.includes(signal.priority)) return false
      if (filter.timeframes && !filter.timeframes.includes(signal.timeframe)) return false
      if (filter.minScore && signal.score < filter.minScore) return false
      if (filter.maxAge) {
        const ageHours = (Date.now() - signal.timestamp.getTime()) / (1000 * 60 * 60)
        if (ageHours > filter.maxAge) return false
      }
      if (filter.categories && !filter.categories.includes(signal.category)) return false
      if (filter.tags && !filter.tags.some(tag => signal.tags.includes(tag))) return false

      return true
    })
  }, [signals, filter])

  const getSignalsByType = useCallback((type: SignalType) => {
    return signals.filter(signal => signal.type === type)
  }, [signals])

  const getSignalsByStrength = useCallback((strength: SignalStrength) => {
    return signals.filter(signal => signal.strength === strength)
  }, [signals])

  const getSignalsByPriority = useCallback((priority: SignalPriority) => {
    return signals.filter(signal => signal.priority === priority)
  }, [signals])

  const markSignalAsRead = useCallback((signalId: string) => {
    setSignals(prev => prev.map(signal => 
      signal.id === signalId 
        ? { ...signal, status: 'triggered' as const }
        : signal
    ))
  }, [])

  const dismissSignal = useCallback((signalId: string) => {
    setSignals(prev => prev.filter(signal => signal.id !== signalId))
  }, [])

  useEffect(() => {
    fetchSignals()
  }, [fetchSignals])

  // Auto-refresh signals every 60 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchSignals()
      }
    }, 60000)

    return () => clearInterval(interval)
  }, [fetchSignals, isLoading])

  return {
    signals,
    filteredSignals,
    isLoading,
    error,
    filter,
    updateFilter,
    refreshSignals,
    getSignalsByType,
    getSignalsByStrength,
    getSignalsByPriority,
    markSignalAsRead,
    dismissSignal
  }
}
