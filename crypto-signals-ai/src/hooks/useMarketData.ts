'use client'

import { useState, useEffect, useCallback } from 'react'
import { MarketData, CryptoAsset, MarketOverview, TimeFrame } from '@/types/market'
import { marketDataService } from '@/services/api/marketDataService'

interface UseMarketDataReturn {
  marketData: MarketData[]
  cryptoAssets: CryptoAsset[]
  marketOverview: MarketOverview | null
  isLoading: boolean
  error: string | null
  refreshData: () => Promise<void>
  updateTimeframe: (timeframe: TimeFrame) => void
  currentTimeframe: TimeFrame
  apiStatus: any
}

// Convert timeframe to days for historical data
const getTimeframeDays = (timeframe: TimeFrame): number => {
  const timeframeDays: Record<TimeFrame, number> = {
    '1m': 1,
    '5m': 1,
    '15m': 1,
    '30m': 1,
    '1h': 1,
    '4h': 7,
    '1d': 30,
    '1w': 90,
    '1M': 365
  }
  return timeframeDays[timeframe] || 7
}

export function useMarketData(symbol: string = 'BTC'): UseMarketDataReturn {
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [cryptoAssets, setCryptoAssets] = useState<CryptoAsset[]>([])
  const [marketOverview, setMarketOverview] = useState<MarketOverview | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentTimeframe, setCurrentTimeframe] = useState<TimeFrame>('1h')
  const [apiStatus, setApiStatus] = useState<any>(null)

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch data from real APIs
      const [
        newCryptoAssets,
        newMarketOverview,
        newMarketData,
        status
      ] = await Promise.all([
        marketDataService.getTopCryptoAssets(100),
        marketDataService.getMarketOverview(),
        marketDataService.getHistoricalData(symbol, getTimeframeDays(currentTimeframe)),
        marketDataService.getApiStatus()
      ])

      setMarketData(newMarketData)
      setCryptoAssets(newCryptoAssets)
      setMarketOverview(newMarketOverview)
      setApiStatus(status)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market data'
      setError(errorMessage)
      console.error('Market data fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [symbol, currentTimeframe])

  const refreshData = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  const updateTimeframe = useCallback((timeframe: TimeFrame) => {
    setCurrentTimeframe(timeframe)
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Auto-refresh data every 2 minutes (to respect rate limits)
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchData()
      }
    }, 120000) // 2 minutes

    return () => clearInterval(interval)
  }, [fetchData, isLoading])

  return {
    marketData,
    cryptoAssets,
    marketOverview,
    isLoading,
    error,
    refreshData,
    updateTimeframe,
    currentTimeframe,
    apiStatus
  }
}
