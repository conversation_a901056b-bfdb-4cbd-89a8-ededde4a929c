'use client'

import { useState, useEffect, useCallback } from 'react'
import { MarketData, CryptoAsset, MarketOverview, TimeFrame } from '@/types/market'
import { marketDataService } from '@/services/api/marketDataService'

interface UseMarketDataReturn {
  marketData: MarketData[]
  cryptoAssets: CryptoAsset[]
  marketOverview: MarketOverview | null
  isLoading: boolean
  error: string | null
  refreshData: () => Promise<void>
  updateTimeframe: (timeframe: TimeFrame) => void
  currentTimeframe: TimeFrame
  apiStatus: any
}

// Convert timeframe to days for historical data
const getTimeframeDays = (timeframe: TimeFrame): number => {
  const timeframeDays: Record<TimeFrame, number> = {
    '1m': 1,
    '5m': 1,
    '15m': 1,
    '30m': 1,
    '1h': 1,
    '4h': 7,
    '1d': 30,
    '1w': 90,
    '1M': 365
  }
  return timeframeDays[timeframe] || 7
}

export function useMarketData(symbol: string = 'BTC'): UseMarketDataReturn {
  const [marketData, setMarketData] = useState<MarketData[]>([])
  const [cryptoAssets, setCryptoAssets] = useState<CryptoAsset[]>([])
  const [marketOverview, setMarketOverview] = useState<MarketOverview | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentTimeframe, setCurrentTimeframe] = useState<TimeFrame>('1h')
  const [apiStatus, setApiStatus] = useState<any>(null)
  const [isClient, setIsClient] = useState(false)

  // Fix hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  const fetchData = useCallback(async () => {
    // Only fetch data on client side to prevent hydration issues
    if (!isClient) return

    try {
      setIsLoading(true)
      setError(null)

      // Fetch data from real APIs with better error handling
      const [
        newCryptoAssets,
        newMarketOverview,
        newMarketData,
        status
      ] = await Promise.allSettled([
        marketDataService.getTopCryptoAssets(20), // Reduced to avoid rate limits
        marketDataService.getMarketOverview(),
        marketDataService.getHistoricalData(symbol, getTimeframeDays(currentTimeframe)),
        marketDataService.getApiStatus()
      ])

      // Handle results with fallbacks
      if (newCryptoAssets.status === 'fulfilled') {
        setCryptoAssets(newCryptoAssets.value)
      } else {
        console.warn('Failed to fetch crypto assets:', newCryptoAssets.reason)
      }

      if (newMarketOverview.status === 'fulfilled') {
        setMarketOverview(newMarketOverview.value)
      } else {
        console.warn('Failed to fetch market overview:', newMarketOverview.reason)
      }

      if (newMarketData.status === 'fulfilled') {
        setMarketData(newMarketData.value)
      } else {
        console.warn('Failed to fetch market data:', newMarketData.reason)
      }

      if (status.status === 'fulfilled') {
        setApiStatus(status.value)
      } else {
        console.warn('Failed to fetch API status:', status.reason)
      }

      // Only set error if ALL requests failed
      const allFailed = [newCryptoAssets, newMarketOverview, newMarketData, status]
        .every(result => result.status === 'rejected')

      if (allFailed) {
        setError('Unable to fetch any market data. Please check your API configuration.')
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch market data'
      setError(errorMessage)
      console.error('Market data fetch error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [symbol, currentTimeframe, isClient])

  const refreshData = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  const updateTimeframe = useCallback((timeframe: TimeFrame) => {
    setCurrentTimeframe(timeframe)
  }, [])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Auto-refresh data every 2 minutes (to respect rate limits)
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isLoading) {
        fetchData()
      }
    }, 120000) // 2 minutes

    return () => clearInterval(interval)
  }, [fetchData, isLoading])

  return {
    marketData,
    cryptoAssets,
    marketOverview,
    isLoading,
    error,
    refreshData,
    updateTimeframe,
    currentTimeframe,
    apiStatus
  }
}
