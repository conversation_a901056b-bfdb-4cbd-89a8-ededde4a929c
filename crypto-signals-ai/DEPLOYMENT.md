# 🚀 Guia de Deploy - Crypto Signals AI

## 📋 Pré-requisitos

### Ambiente Local
- Node.js 18+ 
- npm, yarn, pnpm ou bun
- Git

### Contas Necessárias (Produção)
- Vercel/Netlify (deploy)
- GitHub (código)
- APIs de crypto (Binance, CoinGecko)
- Serviços de notificação (Discord, Telegram)

## 🛠️ Preparação para Deploy

### 1. Verificação do Build Local
```bash
# Teste o build localmente
npm run build
npm start

# Verifique se não há erros
npm run lint
```

### 2. Configuração de Variáveis de Ambiente

Crie `.env.local` para desenvolvimento:
```env
# APIs de Mercado
NEXT_PUBLIC_BINANCE_API_KEY=your_binance_api_key
NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key

# WebSocket URLs
NEXT_PUBLIC_WS_URL=wss://stream.binance.com:9443/ws

# Notificações
NEXT_PUBLIC_DISCORD_WEBHOOK=your_discord_webhook
NEXT_PUBLIC_TELEGRAM_BOT_TOKEN=your_telegram_token

# Configurações
NEXT_PUBLIC_APP_ENV=development
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
```

Para produção (`.env.production`):
```env
NEXT_PUBLIC_APP_ENV=production
NEXT_PUBLIC_API_BASE_URL=https://your-domain.com/api
# ... outras variáveis
```

## 🌐 Deploy na Vercel (Recomendado)

### Método 1: Deploy Automático via GitHub

1. **Conecte o repositório**:
   - Acesse [vercel.com](https://vercel.com)
   - Conecte sua conta GitHub
   - Importe o repositório `crypto-signals-ai`

2. **Configure as variáveis**:
   - Vá em Settings > Environment Variables
   - Adicione todas as variáveis do `.env.production`

3. **Deploy automático**:
   - Cada push na branch `main` fará deploy automático
   - Preview deployments para outras branches

### Método 2: Deploy via CLI

```bash
# Instale a CLI da Vercel
npm i -g vercel

# Faça login
vercel login

# Deploy
vercel

# Para produção
vercel --prod
```

### Configuração Avançada (vercel.json)
```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        }
      ]
    }
  ]
}
```

## 🔧 Deploy na Netlify

### Via GitHub
1. Conecte repositório no [netlify.com](https://netlify.com)
2. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `.next`
   - Node version: 18

### Via CLI
```bash
# Instale a CLI
npm install -g netlify-cli

# Login
netlify login

# Deploy
netlify deploy

# Produção
netlify deploy --prod
```

### Configuração (netlify.toml)
```toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
```

## ☁️ Deploy na AWS

### Usando AWS Amplify

1. **Conecte repositório**:
   ```bash
   npm install -g @aws-amplify/cli
   amplify configure
   amplify init
   ```

2. **Configure build**:
   ```yaml
   # amplify.yml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

### Usando EC2 + Docker

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

```bash
# Build e deploy
docker build -t crypto-signals-ai .
docker run -p 3000:3000 crypto-signals-ai
```

## 🔒 Configurações de Segurança

### Headers de Segurança
```javascript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ]
  },
}
```

### Rate Limiting
```javascript
// middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Implementar rate limiting
  const ip = request.ip ?? '127.0.0.1'
  // ... lógica de rate limiting
  
  return NextResponse.next()
}
```

## 📊 Monitoramento Pós-Deploy

### 1. Health Checks
```javascript
// app/api/health/route.ts
export async function GET() {
  return Response.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  })
}
```

### 2. Error Tracking (Sentry)
```bash
npm install @sentry/nextjs
```

```javascript
// sentry.client.config.js
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
})
```

### 3. Analytics
```javascript
// Google Analytics
import { GoogleAnalytics } from '@next/third-parties/google'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>{children}</body>
      <GoogleAnalytics gaId="GA_MEASUREMENT_ID" />
    </html>
  )
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🚨 Troubleshooting

### Problemas Comuns

1. **Build Failures**:
   ```bash
   # Limpe cache e reinstale
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **Variáveis de Ambiente**:
   - Verifique se todas as variáveis estão definidas
   - Prefixo `NEXT_PUBLIC_` para variáveis client-side

3. **Memory Issues**:
   ```json
   // package.json
   {
     "scripts": {
       "build": "NODE_OPTIONS='--max-old-space-size=4096' next build"
     }
   }
   ```

4. **API Rate Limits**:
   - Implemente cache para reduzir requests
   - Use múltiplas API keys se necessário

### Logs e Debug
```bash
# Vercel logs
vercel logs

# Netlify logs
netlify logs

# Local debug
DEBUG=* npm run dev
```

## 📈 Otimizações de Performance

### 1. Bundle Analysis
```bash
npm install @next/bundle-analyzer
```

```javascript
// next.config.js
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer(nextConfig)
```

### 2. Image Optimization
```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

### 3. Caching Strategy
```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/market-data',
        headers: [
          {
            key: 'Cache-Control',
            value: 's-maxage=30, stale-while-revalidate=60',
          },
        ],
      },
    ]
  },
}
```

## ✅ Checklist de Deploy

- [ ] Testes passando localmente
- [ ] Build sem erros
- [ ] Variáveis de ambiente configuradas
- [ ] Domínio configurado (se aplicável)
- [ ] SSL/HTTPS habilitado
- [ ] Monitoramento configurado
- [ ] Backup strategy definida
- [ ] Performance testada
- [ ] SEO otimizado
- [ ] Acessibilidade verificada

---

**🎉 Parabéns! Sua aplicação Crypto Signals AI está no ar!**

Para suporte, consulte a documentação oficial das plataformas ou abra uma issue no repositório.
