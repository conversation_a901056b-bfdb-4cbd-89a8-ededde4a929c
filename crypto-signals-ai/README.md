# 🚀 Crypto Signals AI - Advanced Trading Signals Platform

Uma aplicação web completa e avançada para geração automática de sinais de trading de criptomoedas, baseada nos 100+ indicadores técnicos mais importantes, com foco em precisão, usabilidade e análise multi-dimensional do mercado crypto.

![Crypto Signals AI](https://img.shields.io/badge/Status-Active-brightgreen)
![Next.js](https://img.shields.io/badge/Next.js-15.3.3-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.0-38bdf8)

## ✨ Características Principais

### 🎯 Sistema de Sinais Inteligente
- **Sinais de Alta Precisão**: Baseados em confluência de 100+ indicadores técnicos
- **Scoring Automático**: Sistema de pontuação de 0-100 para cada sinal
- **Classificação por Força**: Strong, Moderate, Weak com cores intuitivas
- **Múltiplos Timeframes**: Análise simultânea de 1m até 1M
- **Confidence Level**: Baseado no número de indicadores confirmando

### 📊 Dashboard Profissional
- **Market Overview**: Visão geral do mercado com métricas essenciais
- **Heatmap Interativo**: Visualização colorizada de todas as cryptos
- **Score Geral do Mercado**: Índice composto baseado em indicadores macro
- **Alertas Prioritários**: Notificações de alta prioridade em tempo real
- **Estilo Glass Morphism**: Interface moderna com efeito de vidro

### 🔧 Indicadores Técnicos (100+)

#### Momentum
- RSI (Relative Strength Index)
- MACD (Moving Average Convergence Divergence)
- Stochastic Oscillator
- Williams %R
- CCI (Commodity Channel Index)

#### Trend
- SMA/EMA (Simple/Exponential Moving Averages)
- Parabolic SAR
- Ichimoku Cloud
- ADX (Average Directional Index)
- Supertrend

#### Volatilidade
- Bollinger Bands
- ATR (Average True Range)
- Keltner Channels
- Volatility Index

#### Volume
- VWAP (Volume Weighted Average Price)
- MFI (Money Flow Index)
- OBV (On Balance Volume)
- Volume Oscillator

#### Suporte/Resistência
- Pivot Points
- Fibonacci Retracements
- Support/Resistance Levels
- Trend Lines

### 🌐 Funcionalidades Específicas para Crypto

#### Bitcoin Dominance Tracker
- Gráfico em tempo real da dominância
- Alertas para níveis críticos (40%, 50%, 60%)
- Previsão de alt season
- Correlação com performance das altcoins

#### Funding Rate Monitor
- Mapa de calor dos funding rates por exchange
- Alertas para rates extremos (>0.1% ou <-0.1%)
- Análise de sentiment baseada em funding
- Identificação de oportunidades

#### Liquidation Heatmap
- Visualização de liquidações por nível de preço
- Alertas para "liquidation cascades"
- Análise de risco de posições alavancadas
- Identificação de "liquidation magnets"

#### On-Chain Integration
- Exchange inflows/outflows em tempo real
- Whale alerts (movimentações > $1M)
- Network health indicators
- Mining metrics (hash rate, difficulty)

### 🚨 Sistema de Alertas Avançado

#### Tipos de Alertas
- **Push Notifications**: Notificações no browser
- **Email**: Para sinais críticos
- **Webhook**: Discord/Telegram integration
- **SMS**: Para alertas de alto risco

#### Configurações Personalizáveis
- Threshold de score mínimo
- Cryptomoedas específicas
- Horários de funcionamento
- Frequência máxima de alertas

### 📈 Análise Multi-Timeframe
- **Análise Simultânea**: 1m, 5m, 15m, 1h, 4h, 1d, 1w
- **Alignment Score**: Percentual de timeframes concordando
- **Divergence Detection**: Identificação automática de divergências
- **Fractal Analysis**: Padrões repetitivos em diferentes escalas

## 🛠️ Tecnologias Utilizadas

### Frontend
- **Next.js 15.3.3**: Framework React com App Router
- **TypeScript**: Tipagem estática para maior segurança
- **Tailwind CSS**: Estilização utilitária e responsiva
- **shadcn/ui**: Componentes UI modernos e acessíveis
- **Lucide React**: Ícones SVG otimizados

### Bibliotecas Especializadas
- **Recharts**: Gráficos interativos e responsivos
- **date-fns**: Manipulação de datas
- **class-variance-authority**: Variantes de componentes
- **clsx & tailwind-merge**: Utilitários de CSS

### Arquitetura
- **Component-Based**: Arquitetura modular e reutilizável
- **TypeScript Interfaces**: Tipagem forte para dados de mercado
- **Custom Hooks**: Lógica reutilizável para estado e efeitos
- **Service Layer**: Separação de lógica de negócio

## 🚀 Instalação e Execução

### Pré-requisitos
- Node.js 18+
- npm, yarn, pnpm ou bun

### Instalação

```bash
# Clone o repositório
git clone https://github.com/seu-usuario/crypto-signals-ai.git

# Entre no diretório
cd crypto-signals-ai

# Instale as dependências
npm install
# ou
yarn install
# ou
pnpm install
```

### Execução em Desenvolvimento

```bash
npm run dev
# ou
yarn dev
# ou
pnpm dev
```

Abra [http://localhost:3000](http://localhost:3000) no seu navegador.

### Build para Produção

```bash
npm run build
npm start
```

## 📁 Estrutura do Projeto

```
src/
├── components/           # Componentes React
│   ├── dashboard/       # Componentes do dashboard
│   ├── signals/         # Componentes de sinais
│   ├── indicators/      # Componentes de indicadores
│   ├── charts/          # Componentes de gráficos
│   ├── alerts/          # Componentes de alertas
│   ├── settings/        # Componentes de configurações
│   └── ui/              # Componentes UI base
├── hooks/               # Custom React hooks
│   ├── useWebSocket.ts  # Hook para WebSocket
│   ├── useIndicators.ts # Hook para indicadores
│   └── useSignals.ts    # Hook para sinais
├── services/            # Serviços e lógica de negócio
│   ├── indicators/      # Cálculos de indicadores
│   ├── signals/         # Geração de sinais
│   └── api.ts           # Chamadas de API
├── types/               # Definições TypeScript
│   ├── indicators.ts    # Tipos de indicadores
│   ├── signals.ts       # Tipos de sinais
│   └── market.ts        # Tipos de mercado
├── utils/               # Utilitários
│   ├── scoring.ts       # Sistema de pontuação
│   ├── alerts.ts        # Sistema de alertas
│   └── helpers.ts       # Funções auxiliares
└── lib/                 # Bibliotecas e configurações
    └── utils.ts         # Utilitários gerais
```

## 🎨 Componentes Principais

### Dashboard
- **MarketOverview**: Visão geral do mercado
- **SignalCard**: Cards de sinais individuais
- **IndicatorMatrix**: Matriz de indicadores
- **MarketHeatmap**: Heatmap do mercado
- **CryptoMetrics**: Métricas específicas de crypto

### Sinais
- **SignalGenerator**: Gerador de sinais
- **SignalFilter**: Filtros de sinais
- **SignalHistory**: Histórico de sinais

### Indicadores
- **TechnicalIndicators**: Indicadores técnicos
- **AdvancedIndicators**: Indicadores avançados
- **IndicatorCalculator**: Calculadora de indicadores

## 🔧 Configuração

### Variáveis de Ambiente

Crie um arquivo `.env.local`:

```env
# API Keys (quando implementar APIs reais)
NEXT_PUBLIC_BINANCE_API_KEY=your_binance_api_key
NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key

# WebSocket URLs
NEXT_PUBLIC_WS_URL=wss://stream.binance.com:9443/ws

# Configurações de Alertas
NEXT_PUBLIC_DISCORD_WEBHOOK=your_discord_webhook
NEXT_PUBLIC_TELEGRAM_BOT_TOKEN=your_telegram_token
```

### Personalização

#### Indicadores
Para adicionar novos indicadores, edite:
- `src/services/indicators/` - Adicione cálculos
- `src/types/indicators.ts` - Adicione tipos
- `src/components/indicators/` - Adicione componentes

#### Sinais
Para personalizar sinais, edite:
- `src/services/signals/signalGenerator.ts` - Lógica de geração
- `src/types/signals.ts` - Tipos de sinais
- Weights e thresholds no SignalGenerator

## 📊 Dados Mock vs Dados Reais

Atualmente a aplicação usa dados mock para demonstração. Para implementar dados reais:

1. **APIs de Mercado**: Binance, CoinGecko, CoinMarketCap
2. **WebSocket**: Streams em tempo real
3. **On-Chain Data**: APIs como Glassnode, IntoTheBlock
4. **Macro Data**: APIs financeiras tradicionais

## 🎯 Roadmap

### Fase 1 ✅ (Concluída)
- [x] Setup inicial do projeto
- [x] Componentes base do dashboard
- [x] Sistema de indicadores técnicos
- [x] Gerador de sinais
- [x] Interface com estilo glass

### Fase 2 🚧 (Em Desenvolvimento)
- [ ] Integração com APIs reais
- [ ] WebSocket para dados em tempo real
- [ ] Sistema de backtesting
- [ ] Persistência de dados

### Fase 3 📋 (Planejado)
- [ ] Sistema de estratégias customizáveis
- [ ] Machine Learning para sinais
- [ ] Mobile app (React Native)
- [ ] API própria para terceiros

### Fase 4 🔮 (Futuro)
- [ ] Trading automático
- [ ] Portfolio management
- [ ] Social trading features
- [ ] Advanced analytics

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Contato

- **Email**: <EMAIL>
- **LinkedIn**: [Seu LinkedIn](https://linkedin.com/in/seu-perfil)
- **Twitter**: [@seu_twitter](https://twitter.com/seu_twitter)

## 🙏 Agradecimentos

- [Next.js](https://nextjs.org/) - Framework React
- [Tailwind CSS](https://tailwindcss.com/) - CSS Framework
- [shadcn/ui](https://ui.shadcn.com/) - Componentes UI
- [Lucide](https://lucide.dev/) - Ícones
- [Recharts](https://recharts.org/) - Biblioteca de gráficos

---

**⚠️ Disclaimer**: Esta aplicação é apenas para fins educacionais e de demonstração. Não constitui aconselhamento financeiro. Sempre faça sua própria pesquisa antes de tomar decisões de investimento.
