# Crypto Signals AI - Environment Variables

# ==============================================
# API KEYS (Required for production)
# ==============================================

# CoinGecko API Key (Free tier: 10-30 calls/min)
# Get your free API key at: https://www.coingecko.com/en/api/pricing
NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key_here

# CoinMarketCap API Key (Free tier: 30 calls/min, 10k/month)
# Get your free API key at: https://coinmarketcap.com/api/
NEXT_PUBLIC_COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# ==============================================
# OPTIONAL APIS (For enhanced features)
# ==============================================

# Binance API (Free, no key required for public endpoints)
# Used for funding rates and real-time data
# NEXT_PUBLIC_BINANCE_API_KEY=optional_binance_api_key
# NEXT_PUBLIC_BINANCE_SECRET_KEY=optional_binance_secret_key

# Alternative.me Fear & Greed Index (Free, no key required)
# Automatically used for sentiment data

# Blockchain.info API (Free, no key required)
# Used for Bitcoin on-chain metrics

# Mempool.space API (Free, no key required)
# Used for Bitcoin network data

# ==============================================
# NOTIFICATION SERVICES (Optional)
# ==============================================

# Discord Webhook for alerts
NEXT_PUBLIC_DISCORD_WEBHOOK=https://discord.com/api/webhooks/your_webhook_here

# Telegram Bot for notifications
NEXT_PUBLIC_TELEGRAM_BOT_TOKEN=your_telegram_bot_token
NEXT_PUBLIC_TELEGRAM_CHAT_ID=your_telegram_chat_id

# ==============================================
# APPLICATION SETTINGS
# ==============================================

# Environment
NEXT_PUBLIC_APP_ENV=development

# API Base URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api

# Rate Limiting Settings
NEXT_PUBLIC_RATE_LIMIT_ENABLED=true
NEXT_PUBLIC_CACHE_ENABLED=true

# WebSocket Settings (for real-time data)
NEXT_PUBLIC_WS_ENABLED=true
NEXT_PUBLIC_WS_RECONNECT_INTERVAL=5000

# ==============================================
# ANALYTICS & MONITORING (Optional)
# ==============================================

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry for error tracking
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here

# ==============================================
# INSTRUCTIONS
# ==============================================

# 1. Copy this file to .env.local
# 2. Fill in your API keys
# 3. Restart the development server

# Free API Key Sources:
# - CoinGecko: https://www.coingecko.com/en/api/pricing (Demo plan is free)
# - CoinMarketCap: https://coinmarketcap.com/api/ (Basic plan is free)

# Rate Limits (Free Tiers):
# - CoinGecko: 10-30 calls/minute
# - CoinMarketCap: 30 calls/minute, 10,000/month
# - Binance: No rate limit for public endpoints
# - Alternative.me: No rate limit

# The application will work with just CoinGecko OR CoinMarketCap API key
# Both APIs are used as fallbacks for each other

# ==============================================
# PRODUCTION DEPLOYMENT
# ==============================================

# For production deployment, set these in your hosting platform:
# - Vercel: Project Settings > Environment Variables
# - Netlify: Site Settings > Environment Variables
# - AWS: Lambda Environment Variables
# - Docker: docker-compose.yml or Dockerfile ENV

# Security Notes:
# - Never commit .env.local to version control
# - Use NEXT_PUBLIC_ prefix only for client-side variables
# - Keep API keys secure and rotate them regularly
# - Monitor your API usage to avoid rate limits
