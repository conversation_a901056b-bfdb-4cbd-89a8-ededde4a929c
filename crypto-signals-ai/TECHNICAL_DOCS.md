# 📋 Documentação Técnica - Crypto Signals AI

## 🏗️ Arquitetura da Aplicação

### Stack Tecnológico
- **Frontend**: Next.js 15.3.3 com App Router
- **Linguagem**: TypeScript para tipagem estática
- **Estilização**: Tailwind CSS + shadcn/ui
- **Ícones**: Lucide React
- **Gráficos**: Recharts
- **Estado**: React Hooks + Context (preparado para Redux)

### Estrutura de Componentes

```
src/components/
├── ui/                    # Componentes base reutilizáveis
│   ├── card.tsx          # Card com estilo glass
│   ├── badge.tsx         # Badges para status
│   └── loading.tsx       # Estados de carregamento
├── dashboard/            # Componentes do dashboard
│   ├── Dashboard.tsx     # Componente principal
│   ├── MarketOverview.tsx # Visão geral do mercado
│   └── CryptoMetrics.tsx # Métricas específicas
├── signals/              # Sistema de sinais
│   └── SignalCard.tsx    # Card individual de sinal
├── indicators/           # Indicadores técnicos
│   └── IndicatorMatrix.tsx # Matriz de indicadores
├── charts/               # Componentes de gráficos
│   └── MarketHeatmap.tsx # Heatmap do mercado
└── settings/             # Configurações
    ├── AlertSettings.tsx # Config de alertas
    └── StrategySettings.tsx # Config de estratégias
```

## 🔧 Sistema de Indicadores Técnicos

### Categorias Implementadas

#### 1. Momentum (src/services/indicators/technical.ts)
- **RSI (Relative Strength Index)**
  - Período padrão: 14
  - Overbought: > 70
  - Oversold: < 30
  - Retorna: valor, status, strength, metadata

- **MACD (Moving Average Convergence Divergence)**
  - Períodos: 12, 26, 9
  - Detecta: crossovers bullish/bearish
  - Retorna: macd, signal, histogram

- **Stochastic Oscillator**
  - Períodos: %K=14, %D=3
  - Overbought: > 80
  - Oversold: < 20

#### 2. Trend
- **SMA/EMA (Moving Averages)**
- **Parabolic SAR**
- **Ichimoku Cloud**

#### 3. Volatilidade
- **Bollinger Bands**
- **ATR (Average True Range)**

#### 4. Volume
- **VWAP (Volume Weighted Average Price)**
- **MFI (Money Flow Index)**

### Estrutura de Dados dos Indicadores

```typescript
interface IndicatorValue {
  value: number | string | object
  status: 'bullish' | 'bearish' | 'neutral' | 'unknown'
  strength: number // 0-100
  timestamp: Date
  metadata?: Record<string, any>
}
```

## 🚨 Sistema de Sinais

### Geração de Sinais (src/services/signals/signalGenerator.ts)

#### Processo de Geração
1. **Coleta de Indicadores**: Agrega dados de todos os indicadores
2. **Cálculo de Score**: Pontuação baseada em pesos e confluência
3. **Classificação**: Strong (80-100), Moderate (60-79), Weak (40-59)
4. **Validação**: Filtros de qualidade e consistência

#### Pesos dos Indicadores
```typescript
const weights = {
  'RSI': 0.15,
  'MACD': 0.20,
  'BollingerBands': 0.15,
  'Volume': 0.08,
  'SupportResistance': 0.12,
  'MacroSentiment': 0.15,
  // ... outros indicadores
}
```

#### Tipos de Sinais
- **Buy**: Confluência bullish com score > 60
- **Sell**: Confluência bearish com score > 60
- **Alert**: Condições especiais (volatilidade, breakouts)

### Estrutura de Dados dos Sinais

```typescript
interface Signal {
  id: string
  symbol: string
  type: 'buy' | 'sell' | 'alert'
  strength: 'weak' | 'moderate' | 'strong'
  priority: 'low' | 'medium' | 'high' | 'critical'
  score: number // 0-100
  confidence: number // 0-100
  timeframe: TimeFrame
  timestamp: Date
  expiresAt?: Date
  
  // Níveis de preço
  entryPrice?: number
  targetPrice?: number
  stopLoss?: number
  
  // Detalhes
  title: string
  description: string
  reasoning: string[]
  indicators: SignalIndicator[]
  
  // Risk management
  riskReward?: number
  status: 'active' | 'triggered' | 'expired' | 'cancelled'
}
```

## 🎣 Hooks Personalizados

### useMarketData (src/hooks/useMarketData.ts)
Gerencia dados de mercado em tempo real:
- Preços de criptomoedas
- Market overview (cap, volume, dominance)
- Auto-refresh a cada 30 segundos
- Suporte a múltiplos timeframes

### useSignals (src/hooks/useSignals.ts)
Gerencia sistema de sinais:
- Geração automática de sinais
- Filtros avançados
- Agrupamento por tipo/força/prioridade
- Auto-refresh a cada 60 segundos

### useWebSocket (src/hooks/useWebSocket.ts)
Simula conexão WebSocket para dados em tempo real:
- Atualizações de preços
- Novos sinais
- Status de conexão
- Auto-reconexão

### useNotifications (src/hooks/useWebSocket.ts)
Sistema de notificações do browser:
- Permissões automáticas
- Notificações de sinais
- Auto-close após 5 segundos

## 🎨 Sistema de Design

### Glass Morphism
Implementado via CSS customizado:
```css
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}
```

### Cores e Estados
- **Bullish**: Verde (#10B981)
- **Bearish**: Vermelho (#EF4444)
- **Neutral**: Amarelo (#F59E0B)
- **Loading**: Azul (#3B82F6)

### Responsividade
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Grid adaptativo para diferentes tamanhos de tela

## 📊 Dados Mock vs Produção

### Dados Atuais (Mock)
Todos os dados são gerados dinamicamente para demonstração:
- Preços com volatilidade realística
- Indicadores calculados matematicamente
- Sinais baseados em lógica real
- Performance metrics simuladas

### Migração para Produção
Para implementar dados reais:

1. **APIs de Mercado**:
   ```typescript
   // Binance WebSocket
   const ws = new WebSocket('wss://stream.binance.com:9443/ws/btcusdt@ticker')
   
   // CoinGecko REST API
   const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd')
   ```

2. **Cálculo de Indicadores**:
   - Substituir dados mock por dados reais
   - Implementar cache para performance
   - Adicionar validação de dados

3. **WebSocket Real**:
   - Conectar com exchanges reais
   - Implementar rate limiting
   - Adicionar fallback para REST APIs

## 🔒 Segurança e Performance

### Segurança
- Todas as APIs keys devem ser server-side
- Validação de dados de entrada
- Rate limiting para APIs externas
- Sanitização de dados do usuário

### Performance
- Lazy loading de componentes
- Memoização com React.memo
- Virtual scrolling para listas grandes
- Debounce em inputs de busca
- Cache de dados com TTL

### Otimizações Implementadas
- Componentes de loading para UX
- Auto-refresh inteligente (pausa quando inativo)
- Filtros client-side para reduzir requests
- Compressão de dados WebSocket

## 🧪 Testing Strategy

### Testes Recomendados
1. **Unit Tests**: Funções de cálculo de indicadores
2. **Integration Tests**: Hooks e serviços
3. **E2E Tests**: Fluxos principais do usuário
4. **Performance Tests**: Carregamento de dados

### Ferramentas Sugeridas
- Jest + React Testing Library
- Cypress para E2E
- MSW para mock de APIs
- Lighthouse para performance

## 🚀 Deploy e CI/CD

### Build para Produção
```bash
npm run build
npm start
```

### Variáveis de Ambiente
```env
NEXT_PUBLIC_BINANCE_API_KEY=
NEXT_PUBLIC_COINGECKO_API_KEY=
NEXT_PUBLIC_WS_URL=
NEXT_PUBLIC_DISCORD_WEBHOOK=
```

### Plataformas Recomendadas
- **Vercel**: Deploy automático com GitHub
- **Netlify**: Alternativa com edge functions
- **AWS**: Para maior controle e escalabilidade

## 📈 Métricas e Monitoramento

### Métricas de Negócio
- Número de sinais gerados por dia
- Taxa de acerto dos sinais
- Tempo médio de resposta
- Usuários ativos

### Métricas Técnicas
- Performance de carregamento
- Erros de JavaScript
- Uptime da aplicação
- Uso de recursos

### Ferramentas Sugeridas
- Google Analytics para usuários
- Sentry para error tracking
- New Relic para performance
- DataDog para infraestrutura

## 🔄 Roadmap Técnico

### Próximas Implementações
1. **Backend API**: Node.js + Express + PostgreSQL
2. **Autenticação**: NextAuth.js + JWT
3. **Database**: Prisma ORM + PostgreSQL
4. **Cache**: Redis para dados frequentes
5. **Queue**: Bull.js para processamento assíncrono
6. **ML Pipeline**: Python + TensorFlow para sinais avançados

### Melhorias de Performance
1. **Server-Side Rendering**: Para SEO e performance inicial
2. **Edge Computing**: Cloudflare Workers para latência
3. **CDN**: Para assets estáticos
4. **Database Optimization**: Índices e queries otimizadas

---

**Nota**: Esta documentação deve ser atualizada conforme a aplicação evolui. Mantenha sempre sincronizada com o código atual.
