# 🔑 Guia de Configuração das APIs

## 📋 Visão Geral

A aplicação **Crypto Signals AI** agora usa **dados 100% reais** de múltiplas APIs. Este guia te ajudará a configurar todas as APIs necessárias.

## 🎯 APIs Implementadas

### ✅ APIs Obrigatórias (pelo menos uma)

#### 1. CoinGecko API (Recomendado)
- **Custo**: 🆓 Gratuito
- **Rate Limit**: 10-30 calls/minuto
- **Uso**: Dados de mercado, preços, histórico

**Como obter:**
1. Acesse [coingecko.com/en/api/pricing](https://www.coingecko.com/en/api/pricing)
2. Clique em "Get Free Demo API Key"
3. Crie uma conta gratuita
4. Copie sua API key do dashboard

#### 2. CoinMarketCap API (Alternativa)
- **Custo**: 🆓 Gratuito
- **Rate Limit**: 30 calls/minuto, 10k/mês
- **Uso**: Mé<PERSON>as globais, rankings

**Como obter:**
1. Acesse [coinmarketcap.com/api](https://coinmarketcap.com/api/)
2. Clique em "Get Your Free API Key Now"
3. Crie uma conta e verifique email
4. Copie sua API key do dashboard

### ✅ APIs Automáticas (sem configuração)

#### 3. Binance API
- **Custo**: 🆓 Gratuito
- **Rate Limit**: Sem limite para endpoints públicos
- **Uso**: Funding rates, preços em tempo real
- **Status**: ✅ Já configurado

#### 4. Alternative.me Fear & Greed Index
- **Custo**: 🆓 Gratuito
- **Rate Limit**: Sem limite
- **Uso**: Índice de medo e ganância
- **Status**: ✅ Já configurado

#### 5. Blockchain.info
- **Custo**: 🆓 Gratuito
- **Rate Limit**: Sem limite
- **Uso**: Métricas on-chain do Bitcoin
- **Status**: ✅ Já configurado

#### 6. Mempool.space
- **Custo**: 🆓 Gratuito
- **Rate Limit**: Sem limite
- **Uso**: Status da rede Bitcoin
- **Status**: ✅ Já configurado

## ⚙️ Configuração Passo a Passo

### 1. Copie o arquivo de exemplo
```bash
cp .env.example .env.local
```

### 2. Edite o arquivo .env.local
```bash
# Use seu editor preferido
nano .env.local
# ou
code .env.local
```

### 3. Configure as API keys
```env
# Obrigatório: Pelo menos uma das duas
NEXT_PUBLIC_COINGECKO_API_KEY=CG-sua_chave_aqui
NEXT_PUBLIC_COINMARKETCAP_API_KEY=sua_chave_cmc_aqui

# Opcional: Notificações
NEXT_PUBLIC_DISCORD_WEBHOOK=https://discord.com/api/webhooks/...
```

### 4. Reinicie a aplicação
```bash
npm run dev
```

## 🔍 Verificação da Configuração

### Teste 1: Verificar APIs no Console
Abra o DevTools (F12) e verifique se não há erros de API.

### Teste 2: Verificar Dados Reais
- ✅ Preços devem ser atuais
- ✅ Market cap deve ser real
- ✅ Funding rates devem variar
- ✅ Fear & Greed Index deve ser atual

### Teste 3: Verificar Rate Limiting
No console, você deve ver logs como:
```
CoinGecko API: 9/10 tokens available
Cache hit: market_overview
```

## 🚨 Solução de Problemas

### Erro: "API key is required"
**Problema**: API key não configurada
**Solução**: 
1. Verifique se o arquivo `.env.local` existe
2. Confirme que a variável está correta
3. Reinicie o servidor de desenvolvimento

### Erro: "Rate limit exceeded"
**Problema**: Muitas chamadas para a API
**Solução**:
1. A aplicação tem rate limiting automático
2. Aguarde alguns minutos
3. Verifique se não há múltiplas abas abertas

### Erro: "Failed to fetch market data"
**Problema**: Problema de conectividade ou API
**Solução**:
1. Verifique sua conexão com internet
2. Teste a API key em outro serviço
3. Use a API alternativa (CoinGecko ↔ CoinMarketCap)

### Dados não atualizando
**Problema**: Cache muito agressivo
**Solução**:
1. Limpe o cache do browser (Ctrl+F5)
2. Aguarde o próximo refresh automático
3. Use o botão "Refresh" na aplicação

## 📊 Monitoramento de Uso

### CoinGecko
- Dashboard: [coingecko.com/en/api/dashboard](https://www.coingecko.com/en/api/dashboard)
- Limite: 10-30 calls/min
- Reset: A cada minuto

### CoinMarketCap
- Dashboard: [pro.coinmarketcap.com/account](https://pro.coinmarketcap.com/account)
- Limite: 30 calls/min, 10k/mês
- Reset: A cada minuto/mês

## 🎯 Otimizações Implementadas

### Cache Inteligente
```typescript
Preços: 1-2 minutos
Market overview: 5 minutos
Dados históricos: 30 minutos
Informações estáticas: 24 horas
```

### Fallback Automático
- Se CoinGecko falhar → usa CoinMarketCap
- Se CoinMarketCap falhar → usa CoinGecko
- Se ambos falharem → mostra erro amigável

### Rate Limiting
- Token bucket algorithm
- Priorização de requests críticos
- Queue para requests não urgentes

## 🔒 Segurança

### ✅ Boas Práticas Implementadas
- API keys apenas no lado cliente (NEXT_PUBLIC_)
- Sem exposição de chaves sensíveis
- Rate limiting para prevenir abuso
- Cache para reduzir chamadas

### ⚠️ Importante
- Nunca commite `.env.local` no Git
- Monitore o uso das APIs regularmente
- Rotacione as API keys periodicamente

## 🆘 Suporte

### Se ainda tiver problemas:
1. Verifique os logs no console do browser
2. Consulte a documentação oficial das APIs
3. Abra uma issue no repositório
4. Entre em contato com o suporte

### Links Úteis
- [CoinGecko API Docs](https://www.coingecko.com/en/api/documentation)
- [CoinMarketCap API Docs](https://coinmarketcap.com/api/documentation/v1/)
- [Binance API Docs](https://binance-docs.github.io/apidocs/)

---

**🎉 Parabéns! Sua aplicação agora usa dados 100% reais!**
